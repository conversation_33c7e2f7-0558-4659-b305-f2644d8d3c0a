#!/bin/bash

# AI Educational Platform - Setup Script
# This script sets up the entire project for development

echo "🎓 AI Educational Platform - Setup Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
        
        # Check if version is 18 or higher
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR" -ge 18 ]; then
            print_status "Node.js version is compatible"
        else
            print_warning "Node.js version should be 18 or higher"
            print_info "Please update Node.js from https://nodejs.org/"
        fi
    else
        print_error "Node.js not found!"
        print_info "Please install Node.js 18+ from https://nodejs.org/"
        print_info "After installation, run this script again."
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm found: $NPM_VERSION"
    else
        print_error "npm not found!"
        print_info "npm should be installed with Node.js"
        exit 1
    fi
}

# Check if Python is installed (for AI components)
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_status "Python found: $PYTHON_VERSION"
    else
        print_warning "Python3 not found"
        print_info "Python is needed for AI components"
        print_info "Install from https://python.org/"
    fi
}

# Check if MongoDB is installed
check_mongodb() {
    if command -v mongod &> /dev/null; then
        print_status "MongoDB found"
    else
        print_warning "MongoDB not found"
        print_info "Install MongoDB from https://www.mongodb.com/try/download/community"
        print_info "Or use MongoDB Atlas (cloud database)"
    fi
}

# Check if Redis is installed
check_redis() {
    if command -v redis-server &> /dev/null; then
        print_status "Redis found"
    else
        print_warning "Redis not found"
        print_info "Install Redis from https://redis.io/download"
        print_info "Or use Redis Cloud"
    fi
}

# Install root dependencies
install_root_dependencies() {
    print_info "Installing root dependencies..."
    
    if npm install; then
        print_status "Root dependencies installed successfully"
    else
        print_error "Failed to install root dependencies"
        exit 1
    fi
}

# Setup environment files
setup_environment() {
    print_info "Setting up environment files..."
    
    # Copy .env.example to .env if it doesn't exist
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_status "Created .env file from template"
        print_warning "Please update .env with your actual configuration"
    else
        print_status ".env file already exists"
    fi
    
    # Setup environment files for each service
    for service in frontend backend mobile admin; do
        if [ -d "$service" ]; then
            if [ ! -f "$service/.env" ]; then
                if [ -f "$service/.env.example" ]; then
                    cp "$service/.env.example" "$service/.env"
                    print_status "Created $service/.env file"
                fi
            fi
        fi
    done
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    # Create log directories
    mkdir -p logs
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p database/backups
    mkdir -p nginx/logs
    mkdir -p docker/data
    
    print_status "Directories created"
}

# Setup Git hooks (if in a git repository)
setup_git_hooks() {
    if [ -d ".git" ]; then
        print_info "Setting up Git hooks..."
        
        if command -v npx &> /dev/null; then
            npx husky install
            npx husky add .husky/pre-commit "npx lint-staged"
            npx husky add .husky/commit-msg "npx commitlint --edit \$1"
            print_status "Git hooks configured"
        else
            print_warning "npx not found, skipping Git hooks setup"
        fi
    else
        print_warning "Not a Git repository, skipping Git hooks setup"
    fi
}

# Main setup function
main() {
    echo
    print_info "Starting setup process..."
    echo
    
    # Check prerequisites
    print_info "Checking prerequisites..."
    check_nodejs
    check_npm
    check_python
    check_mongodb
    check_redis
    echo
    
    # Install dependencies
    install_root_dependencies
    echo
    
    # Setup environment
    setup_environment
    echo
    
    # Create directories
    create_directories
    echo
    
    # Setup Git hooks
    setup_git_hooks
    echo
    
    # Final instructions
    echo "🎉 Setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Update configuration files:"
    echo "   - .env (main configuration)"
    echo "   - frontend/.env (frontend configuration)"
    echo "   - backend/.env (backend configuration)"
    echo
    echo "2. Install service dependencies:"
    echo "   npm run install:all"
    echo
    echo "3. Start development servers:"
    echo "   npm run dev"
    echo
    echo "4. Access the application:"
    echo "   Frontend:  http://localhost:3000"
    echo "   Backend:   http://localhost:8000"
    echo "   API Docs:  http://localhost:8000/docs"
    echo "   Admin:     http://localhost:3001"
    echo
    echo "📖 For detailed instructions, see README.md"
    echo
}

# Run main function
main
