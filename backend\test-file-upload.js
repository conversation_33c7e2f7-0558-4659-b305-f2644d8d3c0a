/**
 * اختبار رفع الملفات
 */

const fileService = require('./src/services/fileService');
const fs = require('fs').promises;
const path = require('path');

async function testFileUpload() {
  console.log('🧪 اختبار رفع الملفات...\n');

  try {
    // إنشاء ملف نصي تجريبي
    const testContent = `
هذا ملف تجريبي لاختبار قراءة الملفات.

يحتوي هذا الملف على:
1. نص عربي
2. قوائم مرقمة
3. فقرات متعددة

الهدف من هذا الاختبار هو التأكد من أن النظام يقرأ الملفات بشكل صحيح.
    `.trim();

    const testFilePath = path.join(__dirname, 'uploads/temp/test-file.txt');
    
    // التأكد من وجود المجلد
    await fs.mkdir(path.dirname(testFilePath), { recursive: true });
    
    // كتابة الملف التجريبي
    await fs.writeFile(testFilePath, testContent, 'utf8');
    console.log('✅ تم إنشاء ملف تجريبي:', testFilePath);

    // اختبار قراءة الملف
    console.log('\n🔍 اختبار قراءة الملف...');
    const result = await fileService.readPlainText(testFilePath);
    
    console.log('📊 نتائج القراءة:');
    console.log('- المحتوى:', result.content.substring(0, 100) + '...');
    console.log('- عدد الكلمات:', result.wordCount);
    console.log('- عدد الأحرف:', result.charCount);
    console.log('- اللغة المكتشفة:', result.language);
    console.log('- مستوى التعقيد:', result.complexity);
    console.log('- وقت القراءة:', result.readingTime, 'دقيقة');

    // تنظيف الملف التجريبي
    await fs.unlink(testFilePath);
    console.log('\n🧹 تم حذف الملف التجريبي');

    console.log('\n✅ اختبار رفع الملفات نجح!');

  } catch (error) {
    console.error('❌ فشل اختبار رفع الملفات:', error.message);
    console.error('تفاصيل الخطأ:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testFileUpload();
}

module.exports = { testFileUpload };
