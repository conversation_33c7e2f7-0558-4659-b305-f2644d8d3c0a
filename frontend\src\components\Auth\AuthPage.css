/* 🎨 ELEGANT SPLIT LAYOUT DESIGN */

/* Scroll Progress Bar */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
  transform-origin: left;
  z-index: 9999;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Main Container */
.auth-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.4s ease;
  scroll-behavior: smooth;
}

/* Light Theme */
.auth-page.light {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Dark Theme */
.auth-page.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Main Container */
.auth-main-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

/* Ensure page has enough height for scrolling */
.auth-page {
  min-height: 200vh;
}

/* Form Section (Left Side) */
.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: white;
  position: relative;
}

.auth-page.dark .form-section {
  background: #1e293b;
}

.form-left {
  order: 1;
}

.form-right {
  order: 2;
}

/* Form Container */
.form-container {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.6),
    transparent);
  animation: formBorderFlow 3s ease-in-out infinite;
}

@keyframes formBorderFlow {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.auth-page.dark .form-container {
  background: rgba(30, 41, 59, 0.95);
  border-color: rgba(71, 85, 105, 0.3);
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-page.dark .form-title {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: #f1f5f9;
  border-radius: 16px;
  padding: 4px;
  margin-bottom: 2rem;
}

.auth-page.dark .tab-navigation {
  background: #334155;
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: #64748b;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: white;
  color: #1e293b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auth-page.dark .tab-btn {
  color: #94a3b8;
}

.auth-page.dark .tab-btn.active {
  background: #475569;
  color: white;
}

/* Error/Success Messages */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
}

.success-message {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #16a34a;
}

.auth-page.dark .error-message {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #f87171;
}

.auth-page.dark .success-message {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.4);
  color: #4ade80;
}

/* Form */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Input Groups */
.input-group {
  display: flex;
  flex-direction: column;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: #94a3b8;
  z-index: 2;
}

.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  font-size: 16px;
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 0 20px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-page.dark .form-input {
  background: #475569;
  border-color: #64748b;
  color: white;
}

.auth-page.dark .form-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.password-toggle:hover {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Submit Button */
.submit-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  background-size: 200% 200%;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  animation: buttonGradientShift 3s ease-in-out infinite;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
  background-position: 100% 0%;
}

@keyframes buttonGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Forgot Button */
.forgot-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.forgot-btn:hover {
  background: rgba(59, 130, 246, 0.1);
}

.auth-page.dark .forgot-btn {
  color: #60a5fa;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.auth-page.dark .divider::before {
  background: #475569;
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 1rem;
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
}

.auth-page.dark .divider-text {
  background: rgba(30, 41, 59, 0.95);
}

/* Google Button */
.google-btn {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.google-btn:hover {
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.auth-page.dark .google-btn {
  background: #475569;
  border-color: #64748b;
  color: white;
}

.google-icon {
  flex-shrink: 0;
}

/* Logo Section (Right Side) */
.logo-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  position: relative;
  overflow: hidden;
}

.auth-page.dark .logo-section {
  background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
}

.logo-left {
  order: 1;
}

.logo-right {
  order: 2;
}

/* Top Controls */
.top-controls {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  gap: 1rem;
  z-index: 100;
}

.control-btn {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Logo Content */
.logo-content {
  text-align: center;
  color: white;
  z-index: 10;
  position: relative;
}

/* Logo Container */
.logo-container {
  margin-bottom: 3rem;
  position: relative;
}

.logo-icon {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  animation: logoGlow 4s ease-in-out infinite;
}

.logo-icon:hover {
  transform: scale(1.05) rotateY(10deg);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.auth-logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 10px 30px rgba(102, 126, 234, 0.3),
    0 0 0 4px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.auth-logo-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

@keyframes logoGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.4);
  }
}

.sparkle-1, .sparkle-2, .sparkle-3 {
  position: absolute;
  color: rgba(255, 255, 255, 0.8);
  animation: sparkle 2s ease-in-out infinite;
}

.sparkle-1 {
  top: -10px;
  right: -10px;
  animation-delay: 0s;
}

.sparkle-2 {
  bottom: -8px;
  left: -8px;
  animation-delay: 0.7s;
}

.sparkle-3 {
  top: 50%;
  right: -15px;
  animation-delay: 1.4s;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.3) rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
  75% {
    opacity: 0.8;
    transform: scale(1.3) rotate(270deg);
  }
}

/* Welcome Message */
.welcome-message {
  max-width: 500px;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: titleGlow 4s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    text-shadow: 0 2px 20px rgba(255, 255, 255, 0.3);
  }
}

.platform-name {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  line-height: 1.6;
  font-weight: 400;
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.floating-square {
  position: absolute;
  background: rgba(255, 255, 255, 0.08);
  transform: rotate(45deg);
  animation: rotate 8s linear infinite;
}

.square-1 {
  width: 40px;
  height: 40px;
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.square-2 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  right: 30%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-25px) scale(1.1);
    opacity: 0.8;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(45deg) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: rotate(225deg) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: rotate(405deg) scale(1);
    opacity: 0.5;
  }
}

/* Inspiration Section - Beautiful Text with Graphics */
.inspiration-section-vertical {
  padding: 4rem 2rem;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.03) 20%,
    rgba(139, 92, 246, 0.03) 80%,
    transparent 100%);
  position: relative;
  overflow: hidden;
}

.auth-page.dark .inspiration-section-vertical {
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.05) 20%,
    rgba(139, 92, 246, 0.05) 80%,
    transparent 100%);
}

.inspiration-container-vertical {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.inspiration-title-vertical {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  text-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.stats-title-vertical::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.auth-page.dark .stats-title-vertical {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(96, 165, 250, 0.4);
}

.inspiration-list-vertical {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
}

.stats-list-vertical::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 20%,
    rgba(139, 92, 246, 0.3) 80%,
    transparent 100%);
  transform: translateX(-50%);
  z-index: 0;
}

.inspiration-card-vertical {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  gap: 2rem;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  z-index: 1;
  margin-bottom: 3rem;
}

.inspiration-icon-vertical {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.inspiration-content-vertical {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inspiration-title-card {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.inspiration-description {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.inspiration-highlight {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7c3aed;
  background: rgba(124, 58, 237, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
  width: fit-content;
}

.inspiration-graphic-container {
  width: 200px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.inspiration-graphic-container:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05) rotateY(5deg);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.auth-page.dark .inspiration-graphic-container {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.05);
}

.auth-page.dark .inspiration-graphic-container:hover {
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 30px rgba(167, 139, 250, 0.2);
}

.inspiration-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 0 24px 0 100px;
  pointer-events: none;
}

/* Dark mode styles for inspiration cards */
.auth-page.dark .inspiration-card-vertical {
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(75, 85, 99, 0.3);
}

.auth-page.dark .inspiration-title-card {
  color: #60a5fa;
}

.auth-page.dark .inspiration-description {
  color: #9ca3af;
}

.auth-page.dark .inspiration-highlight {
  color: #a78bfa;
  background: rgba(167, 139, 250, 0.1);
}

.stat-card-vertical::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent);
  transition: left 0.6s ease;
}

.stat-card-vertical:hover::before {
  left: 100%;
}

.auth-page.dark .stat-card-vertical {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.stat-icon-vertical {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow:
    0 10px 30px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.stat-icon-vertical::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 70%);
  transform: rotate(-45deg);
  transition: transform 0.6s ease;
}

.stat-card-vertical:hover .stat-icon-vertical::before {
  transform: rotate(-45deg) translate(50%, 50%);
}

.stat-card-vertical:hover .stat-icon-vertical {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 15px 40px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stat-content-vertical {
  flex: 1;
  text-align: left;
}

[dir="rtl"] .stat-content-vertical {
  text-align: right;
}

.stat-value-vertical {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  line-height: 1;
  transition: all 0.4s ease;
}

.stat-card-vertical:hover .stat-value-vertical {
  transform: scale(1.05);
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.stat-label-vertical {
  font-size: 1.25rem;
  font-weight: 600;
  color: #64748b;
  line-height: 1.4;
  transition: all 0.4s ease;
  margin-bottom: 0.5rem;
}

.auth-page.dark .stat-label-vertical {
  color: #94a3b8;
}

.stat-card-vertical:hover .stat-label-vertical {
  color: #3b82f6;
  transform: translateX(5px);
}

[dir="rtl"] .stat-card-vertical:hover .stat-label-vertical {
  transform: translateX(-5px);
}

.stat-description {
  font-size: 0.9rem;
  color: #94a3b8;
  line-height: 1.5;
  font-weight: 400;
  transition: all 0.4s ease;
}

.auth-page.dark .stat-description {
  color: #64748b;
}

.stat-card-vertical:hover .stat-description {
  color: #6b7280;
}

.stat-graphic-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
  height: 120px;
}

.stat-decoration {
  width: 4px;
  height: 60px;
  background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
  opacity: 0.3;
  transition: all 0.4s ease;
}

.stat-card-vertical:hover .stat-decoration {
  opacity: 1;
  height: 80px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Responsive Design for Inspiration Section */
@media (max-width: 768px) {
  .inspiration-section-vertical {
    padding: 2rem 1rem;
  }

  .inspiration-title-vertical {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .inspiration-list-vertical {
    gap: 1.5rem;
  }

  .inspiration-card-vertical {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    text-align: center;
    gap: 1.5rem;
    padding: 2rem 1.5rem;
  }

  .inspiration-icon-vertical {
    width: 60px;
    height: 60px;
    margin: 0 auto;
  }

  .inspiration-content-vertical {
    text-align: center;
    order: 2;
  }

  .inspiration-graphic-container {
    order: 3;
    min-width: 180px;
    height: 110px;
  }

  .stat-content-vertical {
    text-align: center;
    order: 2;
  }

  .stat-graphic-container {
    order: 3;
    min-width: 150px;
    height: 90px;
  }

  .stat-icon-vertical {
    order: 1;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin: 0 auto;
  }

  .stat-decoration {
    order: 4;
    width: 60px;
    height: 4px;
    margin: 0 auto;
  }

  .stat-value-vertical {
    font-size: 2.5rem;
  }

  .stat-label-vertical {
    font-size: 1.1rem;
  }

  .stat-description {
    font-size: 0.85rem;
  }

  .stats-list-vertical::before {
    display: none;
  }
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.stats-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1e293b;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.stats-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
}

.auth-page.dark .stats-title {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.auth-page.dark .stat-card {
  background: #334155;
  border-color: #475569;
}

.auth-page.dark .stat-card:hover {
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.5s ease;
}

.stat-card:hover .stat-icon::before {
  left: 100%;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-value {
  transform: scale(1.05);
}

.auth-page.dark .stat-value {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

.auth-page.dark .stat-label {
  color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .auth-main-container {
    flex-direction: column;
  }

  .form-section,
  .logo-section {
    flex: none;
    min-height: 50vh;
  }

  .form-left,
  .form-right,
  .logo-left,
  .logo-right {
    order: unset;
  }

  .welcome-title {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .form-container {
    padding: 1.5rem;
    margin: 1rem;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .logo-icon {
    width: 80px;
    height: 80px;
  }

  .top-controls {
    top: 1rem;
    right: 1rem;
  }

  .stats-container {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 1rem;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }
}

/* Extra Content for Scrolling */
.extra-content {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(180deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 50%,
    rgba(240, 147, 251, 0.05) 100%);
  overflow: hidden;
}

.extra-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(240, 147, 251, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  animation: floatingBg 20s ease-in-out infinite;
}

@keyframes floatingBg {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.auth-page.dark .extra-content {
  background: linear-gradient(180deg,
    rgba(102, 126, 234, 0.02) 0%,
    rgba(118, 75, 162, 0.02) 50%,
    rgba(240, 147, 251, 0.02) 100%);
}

.auth-page.dark .extra-content::before {
  background:
    radial-gradient(circle at 20% 30%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(240, 147, 251, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
}

.scroll-indicator {
  text-align: center;
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.scroll-text {
  font-size: 0.9rem;
  color: rgba(102, 126, 234, 0.8);
  margin-bottom: 0.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.auth-page.dark .scroll-text {
  color: rgba(167, 139, 250, 0.8);
}

.scroll-arrow {
  font-size: 1.5rem;
  color: rgba(102, 126, 234, 0.6);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scroll-arrow:hover {
  color: rgba(102, 126, 234, 1);
  transform: scale(1.2);
}

.auth-page.dark .scroll-arrow {
  color: rgba(167, 139, 250, 0.6);
}

.auth-page.dark .scroll-arrow:hover {
  color: rgba(167, 139, 250, 1);
}

/* Scroll Progress Bar */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  transform-origin: 0%;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* ========== تصميم الجوال المحسن ========== */
@media (max-width: 768px) {
  .auth-page {
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
  }

  .auth-main-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .form-section {
    width: 100% !important;
    padding: 1.5rem 1rem;
    order: 2;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-section {
    width: 100% !important;
    padding: 2rem 1rem 1rem 1rem;
    order: 1;
    min-height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .form-container {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 1.5rem;
    border-radius: 20px;
  }

  .logo-content {
    text-align: center;
    width: 100%;
  }

  .welcome-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .platform-name {
    font-size: 2.25rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-subtitle {
    font-size: 0.9rem;
    margin-top: 0.5rem;
    opacity: 0.8;
  }

  .top-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    left: auto;
    z-index: 10;
  }

  .control-btn {
    width: 40px !important;
    height: 40px !important;
    margin-left: 0.5rem;
  }

  /* تحسين النموذج للجوال */
  .form-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .tab-navigation {
    margin-bottom: 1.5rem;
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 4px;
  }

  .tab-btn {
    flex: 1;
    padding: 12px 16px !important;
    font-size: 0.9rem;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .input-group {
    margin-bottom: 1rem;
  }

  .input-wrapper {
    position: relative;
    width: 100%;
  }

  .form-input {
    width: 100%;
    padding: 14px 16px 14px 48px !important;
    font-size: 16px !important; /* منع التكبير في iOS */
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: inherit;
    transition: all 0.3s ease;
    -webkit-appearance: none;
    appearance: none;
  }

  .form-input:focus {
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.15);
    outline: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  .input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    z-index: 2;
  }

  .password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
  }

  .password-toggle:hover {
    color: rgba(255, 255, 255, 0.8);
  }

  .submit-btn {
    width: 100%;
    padding: 16px !important;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    margin-top: 1rem;
    min-height: 56px;
    touch-action: manipulation;
  }

  .forgot-btn {
    width: 100%;
    padding: 12px;
    margin-top: 1rem;
    font-size: 0.9rem;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: underline;
    cursor: pointer;
  }

  /* رسائل الخطأ والنجاح */
  .message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
  }

  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
  }

  .success-message {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #86efac;
  }

  /* تحسين الشعار للجوال */
  .logo-container {
    margin-bottom: 1rem;
  }

  .auth-logo-circle {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto 1rem auto;
  }

  .auth-logo-image {
    width: 100%;
    height: 100%;
  }

  /* العناصر المتحركة */
  .floating-elements {
    display: none; /* إخفاء على الجوال لتحسين الأداء */
  }

  .sparkle-1, .sparkle-2, .sparkle-3 {
    display: none;
  }

  /* قسم الإلهام */
  .inspiration-section-vertical {
    padding: 2rem 1rem;
    margin-top: 2rem;
  }

  .inspiration-title-vertical {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .inspiration-card-vertical {
    padding: 1.5rem 1rem;
    margin-bottom: 1.5rem;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .inspiration-icon-vertical {
    margin-bottom: 1rem;
    text-align: center;
  }

  .inspiration-content-vertical {
    text-align: center;
    margin-bottom: 1rem;
  }

  .inspiration-title-card {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  .inspiration-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }

  .inspiration-highlight {
    font-size: 0.8rem;
    font-weight: 600;
    color: #667eea;
  }

  .inspiration-graphic-container {
    width: 60px;
    height: 60px;
    margin: 0 auto;
    opacity: 0.7;
  }

  /* مؤشر التمرير */
  .scroll-indicator {
    padding: 1rem;
    text-align: center;
  }

  .scroll-text {
    font-size: 0.8rem;
    opacity: 0.6;
    margin-bottom: 0.5rem;
  }

  .scroll-arrow {
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.3s ease;
  }

  .scroll-arrow:hover {
    opacity: 1;
    transform: translateY(2px);
  }

  /* شريط التقدم */
  .scroll-progress {
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform-origin: left;
  }

  /* إخفاء شريط التمرير */
  ::-webkit-scrollbar {
    width: 6px;
  }
}

/* الهواتف الصغيرة جداً */
@media (max-width: 480px) {
  .form-section {
    padding: 1rem 0.75rem;
  }

  .logo-section {
    padding: 1.5rem 0.75rem 0.75rem 0.75rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .platform-name {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 0.85rem;
  }

  .form-input {
    padding: 12px 14px 12px 44px !important;
    font-size: 16px !important;
  }

  .submit-btn {
    padding: 14px !important;
    min-height: 52px;
  }

  .inspiration-section-vertical {
    padding: 1.5rem 0.75rem;
  }

  .inspiration-card-vertical {
    padding: 1.25rem 0.75rem;
  }
}

/* تحسينات إضافية للجوال */
@media (max-width: 768px) {
  /* منع التكبير عند التركيز على المدخلات */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    font-size: 16px !important;
    -webkit-appearance: none;
    appearance: none;
  }

  /* تحسين اللمس */
  button, .btn, .tab-btn {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* تحسين الأداء */
  .auth-page * {
    -webkit-overflow-scrolling: touch;
  }

  /* إخفاء شريط التمرير */
  .auth-page::-webkit-scrollbar {
    display: none;
  }

  .auth-page {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}