# AI Educational Platform - Backend

## 🎯 نظرة عامة

Backend API للمنصة التعليمية الذكية التي تستخدم الذكاء الاصطناعي لتوليد الأسئلة، تحليل الأداء، وإنشاء التقارير.

## 🛠️ التقنيات المستخدمة

- **Node.js** - بيئة التشغيل
- **Express.js** - إطار العمل للخادم
- **MongoDB Atlas** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **OpenRouter API** - خدمات الذكاء الاصطناعي
- **JWT** - المصادقة والتفويض
- **Multer** - رفع الملفات
- **Bcrypt** - تشفير كلمات المرور

## 📁 هيكل المشروع

```
backend/
├── src/
│   ├── config/
│   │   └── database.js          # إعداد قاعدة البيانات
│   ├── models/
│   │   ├── User.js              # نموذج المستخدم
│   │   ├── Lecture.js           # نموذج المحاضرات
│   │   ├── Question.js          # نموذج الأسئلة
│   │   ├── Task.js              # نموذج المهام
│   │   ├── Report.js            # نموذج التقارير
│   │   ├── Analytics.js         # نموذج التحليلات
│   │   └── index.js             # تصدير النماذج
│   ├── services/
│   │   ├── aiService.js         # خدمة الذكاء الاصطناعي
│   │   └── fileService.js       # خدمة معالجة الملفات
│   └── server.js                # الخادم الرئيسي
├── uploads/                     # مجلد الملفات المرفوعة
├── test-db.js                   # اختبار قاعدة البيانات
├── test-ai.js                   # اختبار الذكاء الاصطناعي
├── test-complete.js             # اختبار شامل
├── package.json                 # تبعيات المشروع
├── .env                         # متغيرات البيئة
└── README.md                    # هذا الملف
```

## 🚀 التثبيت والتشغيل

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. إعداد متغيرات البيئة
قم بتحديث ملف `.env`:

```env
# Environment
NODE_ENV=development
PORT=5000

# MongoDB Configuration
MONGODB_URI=mongodb+srv://neom:Ahmed13$<EMAIL>/educational_platform?retryWrites=true&w=majority&appName=Cluster0
DB_NAME=educational_platform

# AI Services Configuration
OPENROUTER_API_KEY=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
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_APP_NAME=AI-Educational-Platform

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ADMIN_URL=http://localhost:3001

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
```

### 3. اختبار النظام
```bash
# اختبار قاعدة البيانات
node test-db.js

# اختبار الذكاء الاصطناعي
node test-ai.js

# اختبار شامل
node test-complete.js
```

### 4. تشغيل الخادم
```bash
# وضع التطوير
npm run dev

# وضع الإنتاج
npm start
```

## 📊 قاعدة البيانات

### المجموعات (Collections)

#### Users (المستخدمون)
- معلومات المستخدم الأساسية
- الإحصائيات والنقاط
- التفضيلات (اللغة، المظهر)

#### Lectures (المحاضرات)
- محتوى المحاضرات
- معلومات الملفات
- حالة المعالجة

#### Questions (الأسئلة)
- أسئلة متعددة الأنواع
- الإجابات والتقييمات
- تاريخ المحاولات

#### Tasks (المهام)
- مهام برمجية
- حالات الاختبار
- الحلول والتقييمات

#### Reports (التقارير)
- تقارير الأداء
- التحليلات والتوصيات

#### Analytics (التحليلات)
- إحصائيات يومية
- أنماط الاستخدام
- الإنجازات

## 🤖 خدمة الذكاء الاصطناعي

### النماذج المتاحة
- **النصوص**: microsoft/wizardlm-2-8x22b
- **المحادثة**: meta-llama/llama-3.1-8b-instruct:free
- **التحليل**: google/gemma-2-9b-it:free
- **البرمجة**: qwen/qwen-2.5-coder-32b-instruct

### الوظائف المتاحة
- توليد الأسئلة من المحاضرات
- تحليل أداء الطلاب
- إنشاء التقارير التفصيلية
- مساعدة في المهام البرمجية
- استخراج الكلمات المفتاحية

## 📁 خدمة الملفات

### الأنواع المدعومة
- **المستندات**: PDF, DOC, DOCX, TXT
- **الصور**: JPG, JPEG, PNG, GIF, WEBP
- **الفيديو**: MP4, AVI, MOV, WMV
- **الصوت**: MP3, WAV, OGG

### الميزات
- رفع آمن للملفات
- معالجة وقراءة المحتوى
- إدارة التخزين
- تنظيف الملفات القديمة

## 🔒 الأمان

- **Helmet**: حماية HTTP headers
- **CORS**: تحكم في الوصول
- **Rate Limiting**: تحديد معدل الطلبات
- **Data Sanitization**: تنظيف البيانات
- **JWT**: مصادقة آمنة
- **Bcrypt**: تشفير كلمات المرور

## 📡 API Endpoints

### الصحة والمعلومات
- `GET /health` - فحص صحة النظام
- `GET /api` - معلومات API

### المصادقة والتفويض
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/refresh` - تحديث الرمز المميز
- `POST /api/auth/logout` - تسجيل الخروج
- `GET /api/auth/me` - الحصول على المستخدم الحالي
- `PUT /api/auth/profile` - تحديث الملف الشخصي
- `PUT /api/auth/change-password` - تغيير كلمة المرور

### المستخدمون
- `GET /api/users` - قائمة المستخدمين (مديرين فقط)
- `GET /api/users/:id` - تفاصيل مستخدم
- `PUT /api/users/:id` - تحديث مستخدم (مديرين فقط)
- `DELETE /api/users/:id` - حذف مستخدم (مديرين فقط)
- `GET /api/users/stats/overview` - إحصائيات المستخدمين
- `GET /api/users/leaderboard` - لوحة المتصدرين
- `GET /api/users/search` - البحث عن المستخدمين

### المحاضرات
- `POST /api/lectures` - رفع محاضرة جديدة
- `GET /api/lectures` - قائمة المحاضرات
- `GET /api/lectures/:id` - تفاصيل محاضرة
- `PUT /api/lectures/:id` - تحديث محاضرة
- `DELETE /api/lectures/:id` - حذف محاضرة
- `POST /api/lectures/:id/reprocess` - إعادة معالجة محاضرة

### الأسئلة
- `POST /api/questions/generate` - توليد أسئلة من محاضرة
- `GET /api/questions` - قائمة الأسئلة
- `GET /api/questions/:id` - تفاصيل سؤال
- `POST /api/questions/:id/answer` - الإجابة على سؤال
- `POST /api/questions/:id/rate` - تقييم سؤال
- `DELETE /api/questions/:id` - حذف سؤال
- `GET /api/questions/stats/summary` - إحصائيات الأسئلة

### المهام
- `GET /api/tasks` - قائمة المهام
- `GET /api/tasks/:id` - تفاصيل مهمة
- `POST /api/tasks/:id/submit` - إرسال حل للمهمة
- `POST /api/tasks/:id/help` - طلب مساعدة في المهمة
- `POST /api/tasks/:id/rate` - تقييم مهمة
- `POST /api/tasks` - إنشاء مهمة جديدة (مديرين فقط)

### التقارير
- `POST /api/reports` - إنشاء تقرير جديد
- `GET /api/reports` - قائمة التقارير
- `GET /api/reports/:id` - تفاصيل تقرير
- `POST /api/reports/:id/regenerate` - إعادة توليد تقرير
- `DELETE /api/reports/:id` - حذف تقرير
- `POST /api/reports/:id/share` - مشاركة تقرير
- `GET /api/reports/shared/:token` - عرض تقرير مشارك

### التحليلات
- `GET /api/analytics/daily` - التحليلات اليومية
- `GET /api/analytics/range` - تحليلات لفترة معينة
- `POST /api/analytics/activity` - تحديث النشاط
- `POST /api/analytics/session/start` - بدء جلسة جديدة
- `POST /api/analytics/session/end` - إنهاء الجلسة
- `GET /api/analytics/insights` - تحليل الأداء الذكي
- `GET /api/analytics/achievements` - الحصول على الإنجازات

## 🧪 الاختبارات

### اختبار قاعدة البيانات
```bash
node test-db.js
```

### اختبار الذكاء الاصطناعي
```bash
node test-ai.js
```

### اختبار شامل
```bash
node test-complete.js
```

## 📈 المراقبة

- **Health Check**: `/health`
- **Logging**: Morgan للطلبات
- **Error Handling**: معالجة شاملة للأخطاء
- **Performance**: مراقبة الأداء

## 🔧 التطوير

### إضافة ميزة جديدة
1. إنشاء النموذج في `src/models/`
2. إضافة الخدمة في `src/services/`
3. إنشاء المسارات في `src/routes/`
4. تحديث `server.js`
5. إضافة الاختبارات

### أفضل الممارسات
- استخدام async/await
- معالجة الأخطاء بشكل صحيح
- التحقق من صحة البيانات
- توثيق الكود
- كتابة الاختبارات

## 🚀 النشر

### متطلبات الإنتاج
- Node.js 18+
- MongoDB Atlas
- متغيرات البيئة الصحيحة
- HTTPS للأمان

### خطوات النشر
1. تحديث متغيرات البيئة
2. تشغيل الاختبارات
3. بناء المشروع
4. نشر على الخادم

## 📞 الدعم

للمساعدة والدعم، يرجى التواصل مع فريق التطوير.

---

**تم تطوير هذا المشروع بواسطة فريق المنصة التعليمية الذكية** 🎓
