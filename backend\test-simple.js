/**
 * اختبار بسيط للـ APIs
 */

const axios = require('axios');

async function testBasicAPIs() {
  console.log('🧪 اختبار APIs الأساسية...\n');

  try {
    // 1. اختبار Health Check
    console.log('1️⃣ اختبار Health Check...');
    const healthResponse = await axios.get('http://localhost:5000/health');
    console.log('✅ Health Check نجح:', healthResponse.data.message);

    // 2. اختبار API Info
    console.log('\n2️⃣ اختبار API Info...');
    const apiResponse = await axios.get('http://localhost:5000/api');
    console.log('✅ API Info نجح:', apiResponse.data.name);

    // 3. اختبار تسجيل مستخدم
    console.log('\n3️⃣ اختبار تسجيل مستخدم...');
    const registerData = {
      name: 'مستخدم تجريبي',
      email: `test_${Date.now()}@example.com`,
      password: '123456',
      confirmPassword: '123456'
    };

    try {
      const registerResponse = await axios.post('http://localhost:5000/api/auth/register', registerData);
      console.log('✅ تسجيل المستخدم نجح:', registerResponse.data.message);
      
      const token = registerResponse.data.data.token;
      
      // 4. اختبار الحصول على المستخدم الحالي
      console.log('\n4️⃣ اختبار الحصول على المستخدم الحالي...');
      const meResponse = await axios.get('http://localhost:5000/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ الحصول على المستخدم نجح:', meResponse.data.data.user.name);

      // 5. اختبار قائمة المهام
      console.log('\n5️⃣ اختبار قائمة المهام...');
      const tasksResponse = await axios.get('http://localhost:5000/api/tasks');
      console.log('✅ قائمة المهام نجحت، عدد المهام:', tasksResponse.data.data.tasks.length);

      // 6. اختبار التحليلات
      console.log('\n6️⃣ اختبار التحليلات...');
      const analyticsResponse = await axios.get('http://localhost:5000/api/analytics/daily', {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ التحليلات اليومية نجحت');

      console.log('\n🎉 جميع الاختبارات الأساسية نجحت!');

    } catch (authError) {
      console.error('❌ خطأ في المصادقة:', authError.response?.data || authError.message);
    }

  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  }
}

testBasicAPIs();
