
/* Layout Styles */
.main-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.main-layout.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Background Pattern */
.main-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.main-layout.dark::before {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 80px; /* عرض الـ sidebar المصغر */
  right: 0;
  height: 70px;
  background: #f9fafb;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: calc(100vw - 80px);
}

/* تكييف مع الـ sidebar الموسع */
.sidebar:hover ~ .header {
  left: 280px; /* عرض الـ sidebar الموسع */
  width: calc(100vw - 280px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* للغة العربية */
.main-layout[dir="rtl"] .header {
  left: 0;
  right: 80px;
  width: calc(100vw - 80px);
}

.main-layout[dir="rtl"] .sidebar:hover ~ .header {
  right: 280px;
  width: calc(100vw - 280px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.dark {
  background: #0f172a;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem; /* مسافة متوازنة */
  max-width: 100%;
  margin: 0 auto;
  position: relative;
}

.site-name {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* تقليل المسافة بين الشعار والنص */
  font-size: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  margin-left: 2rem; /* تحريك لليمين (بعيداً عن السايدبار) */
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* عندما يكون السايدبار موسع - تحريك أكثر لليمين */
.sidebar:hover ~ .header .site-name {
  margin-left: 3rem;
}

/* للغة العربية */
.main-layout[dir="rtl"] .site-name {
  margin-left: 0;
  margin-right: 2rem; /* تحريك لليسار (بعيداً عن السايدبار) */
  transition: margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-layout[dir="rtl"] .sidebar:hover ~ .header .site-name {
  margin-right: 3rem;
}

.site-text-container {
  position: relative;
  min-width: 120px;
  height: 2rem;
  display: flex;
  align-items: center;
  overflow: visible;
}

.site-text {
  font-weight: 800;
  letter-spacing: 0.5px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.site-text.abbreviation {
  font-size: 1.5rem;
  justify-content: flex-start;
}

.site-text.single-word {
  font-size: 0.9rem;
  justify-content: flex-start;
  font-weight: 700;
  letter-spacing: 0.8px;
  text-transform: uppercase;
}

.site-text.full-name {
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  font-size: 0.75rem;
  line-height: 1.2;
  gap: 0.1rem;
  white-space: nowrap;
}

.site-text.full-name .word {
  display: block;
  font-weight: 700;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

/* تأثيرات hover للكلمات */
.site-name:hover .site-text.full-name .word {
  transform: translateX(2px);
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.site-name:hover .site-text.abbreviation {
  transform: scale(1.05);
  text-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

.site-name:hover .site-text.single-word {
  transform: scale(1.03) translateX(3px);
  text-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
}

/* تأثير نبض خفيف */
.site-text-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.site-name:hover .site-text-container::before {
  opacity: 1;
}

/* للوضع المظلم */
.main-layout.dark .site-text {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-layout.dark .site-name:hover .site-text.full-name .word {
  text-shadow: 0 2px 4px rgba(167, 139, 250, 0.4);
}

.main-layout.dark .site-name:hover .site-text.abbreviation {
  text-shadow: 0 2px 8px rgba(167, 139, 250, 0.5);
}

.main-layout.dark .site-name:hover .site-text.single-word {
  text-shadow: 0 2px 6px rgba(96, 165, 250, 0.5);
}

.site-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.logo-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15));
}

.logo-image {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 50%;
  background: transparent;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.logo-image:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

.main-layout.dark .logo-image {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.main-layout.dark .logo-image:hover {
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6));
}

/* تم نقل هذا للأعلى */

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.control-btn:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.header.dark .control-btn {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.header.dark .control-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.control-text {
  font-size: 0.875rem;
  font-weight: 600;
}

.notification-btn {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.profile-menu-container {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.profile-btn:hover {
  background: rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  overflow: hidden;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.header.dark .profile-name {
  color: #f3f4f6;
}

.profile-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 0.5rem;
  min-width: 140px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.header.dark .profile-dropdown {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border-radius: 12px;
  background: transparent;
  border: none;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.header.dark .dropdown-item {
  color: #f3f4f6;
}

.dropdown-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
}

.header.dark .dropdown-divider {
  background: rgba(255, 255, 255, 0.1);
}

/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  height: 100vh;
  background: #f9fafb;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  max-height: 100vh;
}

.sidebar.dark {
  background: #0f172a;
}

.sidebar.rtl {
  right: 0;
}

.sidebar-profile {
  padding: 1rem 1.5rem;
  margin-top: 70px;
  flex-shrink: 0;
  overflow: hidden;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar:not(:hover) .sidebar-profile {
  padding: 0.75rem;
  min-height: 60px;
}

.profile-name-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.profile-name-text {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
}

.profile-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-avatar-container {
  position: relative;
}

.profile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.sidebar.dark .profile-name {
  color: #f3f4f6;
}

.profile-role {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.sidebar.dark .profile-role {
  color: #d1d5db;
}

/* Navigation Styles */
.sidebar-nav {
  padding: 0.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 0.5rem;
}

.sidebar:not(:hover) .sidebar-nav {
  justify-content: flex-start;
  padding-top: 1rem;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.625rem;
  border-radius: 12px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  outline: none;
  -webkit-tap-highlight-color: transparent;
  justify-content: flex-start;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
}

.nav-item:hover::before {
  opacity: 1;
  transform: scale(1.02);
}

.nav-item:hover {
  transform: translateX(8px) translateY(-2px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(139, 92, 246, 0.1);
}

.sidebar.rtl .nav-item:hover {
  transform: translateX(-8px) translateY(-2px);
}

.nav-item:active {
  transform: translateX(4px) translateY(0px);
  transition: all 0.1s ease;
}

.sidebar.rtl .nav-item:active {
  transform: translateX(-4px) translateY(0px);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.25),
    0 4px 15px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.sidebar.rtl .nav-item.active {
  transform: translateX(-4px);
}

.nav-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.nav-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 14px;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: all 0.4s ease;
}

.nav-item:hover .nav-icon::before {
  opacity: 1;
  animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);
  }
}

.nav-item.active .nav-icon {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.4),
    0 0 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: activeIconPulse 3s ease-in-out infinite;
}

@keyframes activeIconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 0 25px rgba(255, 255, 255, 0.6),
      0 0 50px rgba(59, 130, 246, 0.5),
      0 0 70px rgba(139, 92, 246, 0.3);
  }
}

.nav-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  flex: 1;
}

.sidebar.dark .nav-label {
  color: #e5e7eb;
}

/* أزرار السايدبار تحتفظ بألوانها في الوضع المظلم */
.nav-item.active .nav-icon {
  color: white !important;
}

.nav-item.active .nav-label {
  color: #3b82f6;
}

/* Nav indicator removed - using glow effects instead */

/* Sidebar Footer */
.sidebar-footer {
  padding: 1.5rem 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  margin-top: auto;
  min-height: 100px;
}

.sidebar:not(:hover) .sidebar-footer {
  min-height: 80px;
  padding: 1rem;
}

.copyright-text {
  text-align: center;
  width: 100%;
}

.copyright-line {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0.3rem 0;
  font-weight: 600;
  line-height: 1.3;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar.dark .copyright-line {
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* رقم الإصدار */
.version-line {
  font-size: 0.75rem;
  color: #94a3b8;
  margin: 0.2rem 0;
  font-weight: 500;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  opacity: 0.8;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

.sidebar.dark .version-line {
  color: #94a3b8;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  opacity: 0.7;
}

.copyright-icon {
  color: #3b82f6;
  opacity: 0.8;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
  transition: all 0.3s ease;
}

.copyright-icon:hover {
  opacity: 1;
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.5));
}

/* Enhanced collapsed sidebar footer */
.sidebar:not(:hover) .copyright-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-radius: 50%;
  padding: 0.75rem;
  box-shadow:
    0 3px 10px rgba(59, 130, 246, 0.15),
    0 1px 5px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar:not(:hover) .copyright-icon:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
  box-shadow:
    0 5px 15px rgba(59, 130, 246, 0.25),
    0 2px 8px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Content Area - حجم ثابت */
.content-area {
  margin-top: 70px;
  margin-left: 80px; /* عرض الـ sidebar المصغر - ثابت */
  padding: 0;
  min-height: calc(100vh - 70px);
  height: auto;
  width: calc(100vw - 80px); /* عرض ثابت */
  overflow: visible;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(1px);
  display: flex;
  flex-direction: column;
}

/* للغة العربية - حجم ثابت */
.main-layout[dir="rtl"] .content-area {
  margin-left: 0;
  margin-right: 80px; /* عرض ثابت */
  width: calc(100vw - 80px); /* عرض ثابت */
}

.main-layout.dark .content-area {
  background: rgba(0, 0, 0, 0.02);
}

/* Section Styles */
.section-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0;
  padding: 2rem;
  box-shadow: none;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: calc(100vh - 70px);
  height: auto;
  width: 100%;
  overflow: visible;
  display: flex;
  flex-direction: column;
  margin: 0;
  flex: 1;
}

.main-layout.dark .section-container {
  background: rgba(15, 23, 42, 0.95);
}

/* Section with internal scroll */
.section-with-scroll {
  overflow-y: auto;
  overflow-x: hidden;
}

.section-with-scroll::-webkit-scrollbar {
  width: 8px;
}

.section-with-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.section-with-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
}

.section-with-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.main-layout.dark .section-with-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.main-layout.dark .section-with-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.4);
}

.main-layout.dark .section-with-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-title {
  font-size: 2.25rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
}

.main-layout.dark .section-subtitle {
  color: #f1f5f9;
}

/* Global text colors for dark mode */
.main-layout.dark h1,
.main-layout.dark h2,
.main-layout.dark h3,
.main-layout.dark h4,
.main-layout.dark h5,
.main-layout.dark h6 {
  color: #ffffff !important;
}

.main-layout.dark p:not(.text-blue-500):not(.text-green-500):not(.text-orange-500):not(.text-purple-500):not(.text-pink-500):not(.text-indigo-500):not(.text-red-500):not(.text-blue-600):not(.text-green-600):not(.text-orange-600):not(.text-purple-600):not(.text-pink-600):not(.text-indigo-600):not(.text-red-600),
.main-layout.dark span:not(.text-blue-500):not(.text-green-500):not(.text-orange-500):not(.text-purple-500):not(.text-pink-500):not(.text-indigo-500):not(.text-red-500):not(.text-blue-600):not(.text-green-600):not(.text-orange-600):not(.text-purple-600):not(.text-pink-600):not(.text-indigo-600):not(.text-red-600),
.main-layout.dark div:not(.text-blue-500):not(.text-green-500):not(.text-orange-500):not(.text-purple-500):not(.text-pink-500):not(.text-indigo-500):not(.text-red-500):not(.text-blue-600):not(.text-green-600):not(.text-orange-600):not(.text-purple-600):not(.text-pink-600):not(.text-indigo-600):not(.text-red-600) {
  color: #f1f5f9 !important;
}

.main-layout.dark .text-gray-600,
.main-layout.dark .text-gray-700,
.main-layout.dark .text-gray-800,
.main-layout.dark .text-gray-900 {
  color: #f1f5f9 !important;
}

.main-layout.dark .text-gray-400,
.main-layout.dark .text-gray-500 {
  color: #e2e8f0 !important;
}

/* Restore original button colors in dark mode */
.main-layout.dark .nav-icon {
  color: inherit !important;
}

.main-layout.dark .text-blue-500,
.main-layout.dark .text-blue-600 {
  color: #3b82f6 !important;
}

.main-layout.dark .text-green-500,
.main-layout.dark .text-green-600 {
  color: #10b981 !important;
}

.main-layout.dark .text-orange-500,
.main-layout.dark .text-orange-600 {
  color: #f59e0b !important;
}

.main-layout.dark .text-purple-500,
.main-layout.dark .text-purple-600 {
  color: #8b5cf6 !important;
}

.main-layout.dark .text-pink-500,
.main-layout.dark .text-pink-600 {
  color: #ec4899 !important;
}

.main-layout.dark .text-indigo-500,
.main-layout.dark .text-indigo-600 {
  color: #6366f1 !important;
}

.main-layout.dark .text-red-500,
.main-layout.dark .text-red-600 {
  color: #ef4444 !important;
}

/* Collapsed Sidebar Styles */
.sidebar:not(:hover) .nav-item {
  justify-content: center;
  padding: 0.5rem;
  margin: 0.2rem 0;
}

.sidebar:not(:hover) .nav-icon {
  width: 32px;
  height: 32px;
  border-radius: 10px;
  box-shadow:
    0 1px 6px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.04);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
}

.sidebar:not(:hover) .nav-item:hover .nav-icon {
  transform: scale(1.1) translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.sidebar:not(:hover) .nav-item.active .nav-icon {
  transform: scale(1.05);
  box-shadow:
    0 0 15px rgba(255, 255, 255, 0.6),
    0 0 30px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
  animation: activeIconGlow 2s ease-in-out infinite;
}

@keyframes activeIconGlow {
  0%, 100% {
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.6),
      0 0 30px rgba(59, 130, 246, 0.4),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      0 0 40px rgba(59, 130, 246, 0.6),
      0 6px 15px rgba(0, 0, 0, 0.2);
  }
}

/* Tooltip for collapsed sidebar */
.sidebar:not(:hover) .nav-item {
  position: relative;
}

.sidebar:not(:hover) .nav-item::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(8px);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(30, 30, 30, 0.95) 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  margin-left: 0.75rem;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar:not(:hover) .nav-item::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(5px);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 8px solid rgba(0, 0, 0, 0.95);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
}

.sidebar.rtl:not(:hover) .nav-item::after {
  left: auto;
  right: 100%;
  margin-left: 0;
  margin-right: 1rem;
  transform: translateY(-50%) translateX(-10px);
}

.sidebar.rtl:not(:hover) .nav-item::before {
  left: auto;
  right: 100%;
  transform: translateY(-50%) translateX(-5px);
  border-right: none;
  border-left: 8px solid rgba(0, 0, 0, 0.95);
}

.sidebar:not(:hover) .nav-item:hover::after,
.sidebar:not(:hover) .nav-item:hover::before {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(0);
}

.sidebar.rtl:not(:hover) .nav-item:hover::after,
.sidebar.rtl:not(:hover) .nav-item:hover::before {
  transform: translateY(-50%) translateX(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 1rem !important;
  }

  .site-name {
    font-size: 1.25rem;
    gap: 0.5rem;
  }

  .site-text-container {
    min-width: 80px;
    height: 1.6rem;
  }

  .site-text.abbreviation {
    font-size: 1.1rem;
  }

  .site-text.single-word {
    font-size: 0.7rem;
    letter-spacing: 0.5px;
  }

  .site-text.full-name {
    font-size: 0.55rem;
    gap: 0.05rem;
  }

  .control-text {
    display: none;
  }

  .profile-name {
    display: none;
  }

  .header {
    left: 60px;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .main-layout[dir="rtl"] .header {
    left: 0;
    right: 60px;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar {
    width: 60px !important;
  }

  .content-area {
    margin-left: 60px;
    padding: 1rem;
  }

  .main-layout[dir="rtl"] .content-area {
    margin-left: 0;
    margin-right: 60px;
  }

  .section-container {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .section-title {
    font-size: 2rem;
  }
}

/* تأثيرات hover للـ sidebar */
.sidebar {
  transition: width 0.3s ease;
}

.sidebar:hover {
  width: 280px !important;
}

.sidebar:not(:hover) {
  width: 80px !important;
}


