# منصة التعليم الذكية - AI Educational Platform

## 📋 نظرة عامة

منصة تعليمية ذكية مدعومة بالذكاء الاصطناعي تهدف إلى تحسين تجربة التعلم من خلال:

- 📚 **شرح المحاضرات**: شرح ذكي وتبسيط للمحاضرات
- ❓ **إنشاء الأسئلة**: توليد أسئلة تفاعلية ومتنوعة
- 📋 **إدارة المهام**: إنشاء وتتبع المهام التعليمية
- 📝 **كتابة التقارير**: مساعدة في كتابة التقارير الأكاديمية
- 📊 **التحليلات**: تحليل الأداء والتقدم في التعلم

## 🏗️ هيكلية المشروع

```
ai-educational-platform/
├── 📁 frontend/          # React Frontend Application
├── 📁 backend/           # Node.js/Express Backend API
├── 📁 database/          # Database schemas and migrations
├── 📁 mobile/            # React Native Mobile App
├── 📁 admin/             # Admin Dashboard
├── 📁 docs/              # Documentation
├── 📁 scripts/           # Build and deployment scripts
├── 📁 config/            # Configuration files
├── 📁 docker/            # Docker configurations
├── 📁 nginx/             # Nginx configurations
└── 📄 package.json       # Root package configuration
```

## 🚀 متطلبات النظام

### الأدوات المطلوبة:
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **Python** >= 3.9 (للذكاء الاصطناعي)
- **MongoDB** >= 6.0 (قاعدة البيانات)
- **Redis** >= 7.0 (للتخزين المؤقت)
- **Docker** (اختياري للنشر)

### خدمات خارجية:
- **Firebase** (للمصادقة والتخزين)
- **OpenAI API** (للذكاء الاصطناعي)
- **Google AI API** (للذكاء الاصطناعي)

## 📦 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/ai-educational-platform.git
cd ai-educational-platform
```

### 2. تثبيت Node.js
قم بتحميل وتثبيت Node.js من: https://nodejs.org/

### 3. تثبيت جميع الحزم
```bash
npm run install:all
```

### 4. إعداد متغيرات البيئة
```bash
npm run setup:env
```

### 5. تشغيل المشروع
```bash
npm run dev
```

## 🔧 الأوامر المتاحة

### تطوير:
- `npm run dev` - تشغيل جميع الخدمات
- `npm run dev:frontend` - تشغيل Frontend فقط
- `npm run dev:backend` - تشغيل Backend فقط
- `npm run dev:mobile` - تشغيل Mobile App فقط
- `npm run dev:admin` - تشغيل Admin Dashboard فقط

### بناء:
- `npm run build` - بناء جميع التطبيقات
- `npm run build:frontend` - بناء Frontend
- `npm run build:backend` - بناء Backend

### اختبار:
- `npm test` - تشغيل جميع الاختبارات
- `npm run test:frontend` - اختبار Frontend
- `npm run test:backend` - اختبار Backend

### Docker:
- `npm run docker:build` - بناء Docker images
- `npm run docker:up` - تشغيل مع Docker
- `npm run docker:down` - إيقاف Docker containers

## 🌐 الوصول للتطبيق

بعد تشغيل المشروع:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Admin Dashboard**: http://localhost:3001
- **Mobile App**: Expo Go أو محاكي

## 📚 التقنيات المستخدمة

### Frontend:
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - للتحقق من الأنواع
- **Vite** - أداة البناء
- **Tailwind CSS** - للتصميم
- **React Query** - لإدارة البيانات
- **React Router** - للتنقل

### Backend:
- **Node.js** - بيئة التشغيل
- **Express.js** - إطار العمل
- **TypeScript** - للتحقق من الأنواع
- **MongoDB** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **JWT** - للمصادقة
- **Socket.io** - للاتصال المباشر

### Mobile:
- **React Native** - تطوير التطبيقات المحمولة
- **Expo** - منصة التطوير
- **TypeScript** - للتحقق من الأنواع

### AI & ML:
- **OpenAI GPT** - للذكاء الاصطناعي
- **Google Gemini** - للذكاء الاصطناعي
- **Python** - للمعالجة المتقدمة
- **TensorFlow** - للتعلم الآلي

### DevOps:
- **Docker** - للحاويات
- **Nginx** - خادم الويب
- **GitHub Actions** - CI/CD
- **MongoDB Atlas** - قاعدة البيانات السحابية

## 🔐 الأمان

- مصادقة JWT آمنة
- تشفير كلمات المرور
- حماية CORS
- تحقق من صحة البيانات
- حماية من هجمات XSS و CSRF

## 📖 الوثائق

- [دليل المطور](docs/developer/README.md)
- [دليل المستخدم](docs/user/README.md)
- [وثائق API](docs/api/README.md)
- [دليل النشر](docs/deployment/README.md)

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للنقاشات العامة

---

**🎓 منصة التعليم الذكية - تعلم بذكاء، تقدم بثقة**
