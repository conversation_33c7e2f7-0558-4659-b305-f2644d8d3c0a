const axios = require('axios');
require('dotenv').config();

async function testOpenRouter() {
  try {
    console.log('🔍 اختبار اتصال OpenRouter API...');
    
    const apiKey = process.env.OPENROUTER_API_KEY;
    console.log('🔑 API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'غير موجود');
    
    const client = axios.create({
      baseURL: 'https://openrouter.ai/api/v1',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:5173',
        'X-Title': 'AI-Educational-Platform'
      },
      timeout: 30000
    });

    const requestData = {
      model: 'meta-llama/llama-3.1-8b-instruct:free',
      messages: [
        {
          role: 'user',
          content: 'مرحبا، هل يمكنك الرد بكلمة واحدة فقط؟'
        }
      ],
      max_tokens: 50,
      temperature: 0.7
    };

    console.log('📤 إرسال طلب اختبار...');
    const response = await client.post('/chat/completions', requestData);
    
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      const result = response.data.choices[0].message.content;
      console.log('✅ نجح الاختبار!');
      console.log('📝 الرد:', result);
      console.log('🔧 النموذج:', response.data.model);
      console.log('📊 الاستخدام:', response.data.usage);
    } else {
      console.log('❌ فشل الاختبار: لا يوجد رد صحيح');
      console.log('📄 البيانات:', response.data);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار OpenRouter:', error.message);
    
    if (error.response) {
      console.error('📄 رد الخطأ:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('📡 لا يوجد رد من الخادم');
    } else {
      console.error('⚙️ خطأ في الإعداد:', error.message);
    }
  }
}

testOpenRouter();
