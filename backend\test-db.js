/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

require('dotenv').config();
const databaseConfig = require('./src/config/database');
const { User, Lecture, Question, Task, Report, Analytics } = require('./src/models');

async function testDatabase() {
  console.log('🧪 بدء اختبار قاعدة البيانات...\n');
  
  try {
    // 1. اختبار الاتصال
    console.log('1️⃣ اختبار الاتصال بقاعدة البيانات...');
    await databaseConfig.connect();
    console.log('✅ تم الاتصال بنجاح\n');
    
    // 2. اختبار النماذج
    console.log('2️⃣ اختبار النماذج...');
    
    // اختبار نموذج المستخدم
    console.log('👤 اختبار نموذج المستخدم...');
    const testUser = new User({
      name: 'مستخدم تجريبي',
      email: '<EMAIL>',
      password: '123456'
    });
    
    // التحقق من صحة البيانات
    const userValidation = testUser.validateSync();
    if (userValidation) {
      console.log('❌ خطأ في التحقق من بيانات المستخدم:', userValidation.message);
    } else {
      console.log('✅ نموذج المستخدم صحيح');
    }
    
    // اختبار نموذج المحاضرة
    console.log('📚 اختبار نموذج المحاضرة...');
    const testLecture = new Lecture({
      userId: testUser._id,
      title: 'محاضرة تجريبية',
      content: 'محتوى المحاضرة التجريبية',
      fileUrl: '/uploads/test.pdf',
      fileName: 'test.pdf',
      fileType: 'pdf',
      fileSize: 1024
    });
    
    const lectureValidation = testLecture.validateSync();
    if (lectureValidation) {
      console.log('❌ خطأ في التحقق من بيانات المحاضرة:', lectureValidation.message);
    } else {
      console.log('✅ نموذج المحاضرة صحيح');
    }
    
    // اختبار نموذج السؤال
    console.log('❓ اختبار نموذج السؤال...');
    const testQuestion = new Question({
      userId: testUser._id,
      lectureId: testLecture._id,
      questionText: 'ما هو JavaScript؟',
      questionType: 'multiple_choice',
      options: [
        { text: 'لغة برمجة', isCorrect: true },
        { text: 'قاعدة بيانات', isCorrect: false },
        { text: 'نظام تشغيل', isCorrect: false }
      ],
      correctAnswer: 'لغة برمجة'
    });
    
    const questionValidation = testQuestion.validateSync();
    if (questionValidation) {
      console.log('❌ خطأ في التحقق من بيانات السؤال:', questionValidation.message);
    } else {
      console.log('✅ نموذج السؤال صحيح');
    }
    
    // اختبار نموذج المهمة
    console.log('💻 اختبار نموذج المهمة...');
    const testTask = new Task({
      title: 'مهمة تجريبية',
      description: 'وصف المهمة التجريبية',
      category: 'web_development',
      language: 'javascript',
      difficulty: 'beginner',
      testCases: [
        {
          input: '5',
          expectedOutput: '25'
        }
      ],
      solution: 'function square(n) { return n * n; }'
    });
    
    const taskValidation = testTask.validateSync();
    if (taskValidation) {
      console.log('❌ خطأ في التحقق من بيانات المهمة:', taskValidation.message);
    } else {
      console.log('✅ نموذج المهمة صحيح');
    }
    
    // اختبار نموذج التقرير
    console.log('📊 اختبار نموذج التقرير...');
    const testReport = new Report({
      userId: testUser._id,
      title: 'تقرير تجريبي',
      content: 'محتوى التقرير التجريبي',
      dateRange: {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31')
      }
    });
    
    const reportValidation = testReport.validateSync();
    if (reportValidation) {
      console.log('❌ خطأ في التحقق من بيانات التقرير:', reportValidation.message);
    } else {
      console.log('✅ نموذج التقرير صحيح');
    }
    
    // اختبار نموذج التحليلات
    console.log('📈 اختبار نموذج التحليلات...');
    const testAnalytics = new Analytics({
      userId: testUser._id,
      date: new Date()
    });
    
    const analyticsValidation = testAnalytics.validateSync();
    if (analyticsValidation) {
      console.log('❌ خطأ في التحقق من بيانات التحليلات:', analyticsValidation.message);
    } else {
      console.log('✅ نموذج التحليلات صحيح');
    }
    
    console.log('\n3️⃣ اختبار العمليات...');
    
    // اختبار Virtual fields
    console.log('🔗 اختبار الحقول الافتراضية...');
    console.log(`مستوى المستخدم: ${testUser.level}`);
    console.log(`معدل نجاح السؤال: ${testQuestion.successRate}%`);
    console.log(`معدل نجاح المهمة: ${testTask.successRate}%`);
    
    // اختبار Methods
    console.log('⚙️ اختبار الطرق...');
    testUser.addPoints(50);
    console.log(`نقاط المستخدم بعد الإضافة: ${testUser.stats.totalPoints}`);
    
    testQuestion.answerQuestion('لغة برمجة', 30);
    console.log(`حالة الإجابة: ${testQuestion.isCorrect ? 'صحيحة' : 'خاطئة'}`);
    
    console.log('\n✅ تم اجتياز جميع الاختبارات بنجاح!');
    
    // 4. عرض معلومات قاعدة البيانات
    console.log('\n4️⃣ معلومات قاعدة البيانات:');
    const dbInfo = databaseConfig.getConnectionInfo();
    if (dbInfo) {
      console.log(`📊 اسم قاعدة البيانات: ${dbInfo.name}`);
      console.log(`🌐 الخادم: ${dbInfo.host}:${dbInfo.port}`);
      console.log(`🔗 حالة الاتصال: ${dbInfo.readyState === 1 ? 'متصل' : 'غير متصل'}`);
      console.log(`📁 المجموعات: ${dbInfo.collections.length > 0 ? dbInfo.collections.join(', ') : 'لا توجد مجموعات'}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار قاعدة البيانات:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    // إغلاق الاتصال
    console.log('\n🔌 إغلاق الاتصال...');
    await databaseConfig.disconnect();
    console.log('✅ تم إغلاق الاتصال بنجاح');
  }
}

// تشغيل الاختبار
testDatabase();
