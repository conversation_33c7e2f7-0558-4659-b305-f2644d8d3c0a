/**
 * اختبار خدمة الذكاء الاصطناعي
 * AI Service Test
 */

require('dotenv').config();
const aiService = require('./src/services/aiService');

async function testAIService() {
  console.log('🤖 بدء اختبار خدمة الذكاء الاصطناعي...\n');
  
  try {
    // 1. اختبار الاتصال
    console.log('1️⃣ اختبار الاتصال...');
    const connectionTest = await aiService.testConnection();
    
    if (connectionTest.success) {
      console.log('✅ الاتصال يعمل بشكل صحيح');
      console.log(`📝 الرد: ${connectionTest.details.content?.substring(0, 100)}...`);
    } else {
      console.log('❌ فشل في الاتصال:', connectionTest.message);
      return;
    }
    
    console.log('\n2️⃣ اختبار توليد الأسئلة...');
    
    // محتوى تجريبي للمحاضرة
    const lectureContent = `
JavaScript هي لغة برمجة عالية المستوى ومفسرة. تُستخدم بشكل أساسي في تطوير المواقع الإلكترونية لإضافة التفاعل والديناميكية.

الخصائص الرئيسية لـ JavaScript:
1. لغة مفسرة - لا تحتاج إلى تجميع
2. ديناميكية - يمكن تغيير نوع المتغيرات أثناء التشغيل
3. كائنية التوجه - تدعم البرمجة الكائنية
4. وظيفية - تدعم البرمجة الوظيفية

المتغيرات في JavaScript:
- var: نطاق الدالة
- let: نطاق الكتلة
- const: ثابت لا يمكن تغييره

الدوال في JavaScript:
function myFunction() {
  return "Hello World";
}

الكائنات:
const person = {
  name: "أحمد",
  age: 25,
  city: "الرياض"
};
`;

    const questionsResult = await aiService.generateQuestions(lectureContent, {
      questionCount: 3,
      difficulty: 'medium',
      questionTypes: ['multiple_choice', 'true_false'],
      language: 'ar'
    });
    
    if (questionsResult.success) {
      console.log('✅ تم توليد الأسئلة بنجاح');
      console.log(`📊 النموذج المستخدم: ${questionsResult.model}`);
      console.log(`🔢 عدد الرموز: ${questionsResult.metadata.tokensUsed}`);
      
      try {
        const questions = JSON.parse(questionsResult.content);
        console.log(`📝 عدد الأسئلة المولدة: ${questions.questions?.length || 0}`);
        
        if (questions.questions && questions.questions.length > 0) {
          console.log('\n📋 مثال على سؤال مولد:');
          const firstQuestion = questions.questions[0];
          console.log(`❓ السؤال: ${firstQuestion.questionText}`);
          console.log(`📝 النوع: ${firstQuestion.questionType}`);
          console.log(`✅ الإجابة الصحيحة: ${firstQuestion.correctAnswer}`);
        }
      } catch (parseError) {
        console.log('⚠️ تم توليد الأسئلة ولكن بصيغة غير JSON صحيحة');
        console.log('📄 المحتوى:', questionsResult.content.substring(0, 200) + '...');
      }
    } else {
      console.log('❌ فشل في توليد الأسئلة:', questionsResult.error);
    }
    
    console.log('\n3️⃣ اختبار استخراج الكلمات المفتاحية...');
    
    const keywordsResult = await aiService.extractKeywordsAndSummary(lectureContent, {
      language: 'ar',
      maxKeywords: 5
    });
    
    if (keywordsResult.success) {
      console.log('✅ تم استخراج الكلمات المفتاحية بنجاح');
      
      try {
        const analysis = JSON.parse(keywordsResult.content);
        console.log(`📝 الملخص: ${analysis.summary}`);
        console.log(`🎯 الموضوع الرئيسي: ${analysis.mainTopic}`);
        console.log(`📊 مستوى الصعوبة: ${analysis.difficulty}`);
        
        if (analysis.keywords && analysis.keywords.length > 0) {
          console.log('\n🔑 الكلمات المفتاحية:');
          analysis.keywords.forEach((keyword, index) => {
            console.log(`  ${index + 1}. ${keyword.word} (تكرار: ${keyword.frequency})`);
          });
        }
      } catch (parseError) {
        console.log('⚠️ تم الاستخراج ولكن بصيغة غير JSON صحيحة');
        console.log('📄 المحتوى:', keywordsResult.content.substring(0, 200) + '...');
      }
    } else {
      console.log('❌ فشل في استخراج الكلمات المفتاحية:', keywordsResult.error);
    }
    
    console.log('\n4️⃣ اختبار المساعدة في البرمجة...');
    
    const codingHelp = await aiService.helpWithCodingTask(
      'اكتب دالة لحساب مضروب العدد',
      `function factorial(n) {
  if (n = 0) {
    return 1;
  }
  return n * factorial(n - 1);
}`,
      'SyntaxError: Invalid left-hand side in assignment',
      {
        language: 'ar',
        programmingLanguage: 'javascript'
      }
    );
    
    if (codingHelp.success) {
      console.log('✅ تم تقديم المساعدة البرمجية بنجاح');
      
      try {
        const help = JSON.parse(codingHelp.content);
        console.log(`🔍 التحليل: ${help.analysis}`);
        console.log(`⚠️ المشاكل: ${help.issues?.join(', ')}`);
        console.log(`💡 الاقتراحات: ${help.suggestions?.join(', ')}`);
      } catch (parseError) {
        console.log('⚠️ تم تقديم المساعدة ولكن بصيغة غير JSON صحيحة');
        console.log('📄 المحتوى:', codingHelp.content.substring(0, 200) + '...');
      }
    } else {
      console.log('❌ فشل في تقديم المساعدة البرمجية:', codingHelp.error);
    }
    
    console.log('\n5️⃣ اختبار تحليل الأداء...');
    
    const performanceData = {
      totalQuestions: 20,
      correctAnswers: 15,
      totalTasks: 10,
      completedTasks: 7,
      timeSpent: 120, // دقيقة
      categories: {
        'web_development': 80,
        'algorithms': 60,
        'database': 90
      },
      recentScores: [85, 90, 75, 95, 80]
    };
    
    const analysisResult = await aiService.analyzeStudentPerformance(performanceData, {
      language: 'ar',
      includeRecommendations: true
    });
    
    if (analysisResult.success) {
      console.log('✅ تم تحليل الأداء بنجاح');
      
      try {
        const analysis = JSON.parse(analysisResult.content);
        console.log(`📊 الدرجة الإجمالية: ${analysis.overallPerformance?.score}`);
        console.log(`🎯 التقدير: ${analysis.overallPerformance?.grade}`);
        console.log(`💪 نقاط القوة: ${analysis.strengths?.join(', ')}`);
        console.log(`⚠️ نقاط الضعف: ${analysis.weaknesses?.join(', ')}`);
      } catch (parseError) {
        console.log('⚠️ تم التحليل ولكن بصيغة غير JSON صحيحة');
        console.log('📄 المحتوى:', analysisResult.content.substring(0, 200) + '...');
      }
    } else {
      console.log('❌ فشل في تحليل الأداء:', analysisResult.error);
    }
    
    console.log('\n✅ تم اكتمال جميع اختبارات الذكاء الاصطناعي!');
    
    // عرض معلومات النماذج المستخدمة
    console.log('\n📋 النماذج المتاحة:');
    console.log(`💬 المحادثة: ${aiService.models.chat}`);
    console.log(`📝 النصوص: ${aiService.models.text}`);
    console.log(`📊 التحليل: ${aiService.models.analysis}`);
    console.log(`💻 البرمجة: ${aiService.models.coding}`);
    
  } catch (error) {
    console.error('❌ خطأ في اختبار الذكاء الاصطناعي:', error.message);
    if (error.response) {
      console.error('📄 تفاصيل الخطأ:', error.response.data);
    }
  }
}

// تشغيل الاختبار
testAIService();
