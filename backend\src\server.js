const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const hpp = require('hpp');
require('dotenv').config();

// استيراد إعداد قاعدة البيانات
const databaseConfig = require('./config/database');

// إنشاء تطبيق Express
const app = express();

/**
 * إعداد Middleware الأمان
 */
// Helmet للأمان
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS
const corsOptions = {
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:5173',
    process.env.ADMIN_URL || 'http://localhost:3001'
  ],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Rate Limiting
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15 دقيقة
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 100, // الحد الأقصى للطلبات
  message: {
    error: 'تم تجاوز الحد الأقصى للطلبات، يرجى المحاولة لاحقاً',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Data sanitization ضد NoSQL injection
app.use(mongoSanitize());

// منع HTTP Parameter Pollution
app.use(hpp());

// ضغط الاستجابات
app.use(compression());

/**
 * إعداد Middleware العام
 */
// Body parsing
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

/**
 * الصحة والمعلومات
 */
// Health check endpoint
app.get('/health', (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: new Date().toISOString(),
    database: databaseConfig.isConnected() ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV || 'development'
  };
  
  res.status(200).json(healthCheck);
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'AI Educational Platform API',
    version: '1.0.0',
    description: 'Backend API for AI Educational Platform',
    endpoints: {
      health: '/health',
      api: '/api',
      auth: '/api/auth',
      users: '/api/users',
      lectures: '/api/lectures',
      questions: '/api/questions',
      tasks: '/api/tasks',
      reports: '/api/reports',
      analytics: '/api/analytics'
    },
    database: databaseConfig.getConnectionInfo()
  });
});

/**
 * Routes
 */
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/lectures', require('./routes/lectures'));
app.use('/api/questions', require('./routes/questions'));
app.use('/api/tasks', require('./routes/tasks'));
app.use('/api/reports', require('./routes/reports'));
app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/statistics', require('./routes/statistics'));
app.use('/api/data', require('./routes/data-collection'));
app.use('/api/chats', require('./routes/chats'));
app.use('/api/files', require('./routes/files'));

/**
 * معالجة الأخطاء
 */
// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    path: req.originalUrl
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err);
  
  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(e => e.message);
    return res.status(400).json({
      success: false,
      message: 'خطأ في التحقق من البيانات',
      errors
    });
  }
  
  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} موجود مسبقاً`
    });
  }
  
  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'رمز المصادقة غير صحيح'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'انتهت صلاحية رمز المصادقة'
    });
  }
  
  // Default error
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || 'خطأ داخلي في الخادم',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

/**
 * بدء الخادم
 */
const PORT = process.env.PORT || 5000;

async function startServer() {
  try {
    // محاولة الاتصال بقاعدة البيانات
    console.log('🔄 بدء الاتصال بقاعدة البيانات...');
    const dbConnected = await databaseConfig.connect();

    // بدء الخادم سواء نجح الاتصال بقاعدة البيانات أم لا
    const server = app.listen(PORT, () => {
      console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
      console.log(`🌐 البيئة: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`📋 API Info: http://localhost:${PORT}/api`);

      if (dbConnected) {
        console.log(`✅ النظام جاهز مع قاعدة البيانات MongoDB Atlas`);
      } else {
        console.log(`⚠️ النظام يعمل بدون قاعدة البيانات - بعض الوظائف قد لا تعمل`);
        console.log(`💡 يمكنك إعادة تشغيل الخادم عند إصلاح مشكلة قاعدة البيانات`);
      }
    });

    // معالجة إغلاق الخادم بشكل صحيح
    process.on('SIGTERM', () => {
      console.log('🔄 تم استلام SIGTERM، إغلاق الخادم...');
      server.close(async () => {
        console.log('🔌 تم إغلاق الخادم');
        if (dbConnected) {
          await databaseConfig.disconnect();
        }
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('🔄 تم استلام SIGINT، إغلاق الخادم...');
      server.close(async () => {
        console.log('🔌 تم إغلاق الخادم');
        if (dbConnected) {
          await databaseConfig.disconnect();
        }
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ فشل في بدء الخادم:', error.message);
    process.exit(1);
  }
}

// بدء الخادم
startServer();

module.exports = app;
