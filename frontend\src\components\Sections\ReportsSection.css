/* Reports Section Styles */

.reports-container {
  height: calc(100vh - 70px);
  width: 100%;
  position: relative;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: calc(100vh - 70px);
}

.reports-container.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* منطقة المحتوى الرئيسي مع scroll محسن */
.reports-content {
  height: calc(100vh - 70px);
  max-height: calc(100vh - 70px);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  gap: 1.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.6) rgba(0, 0, 0, 0.05);
}

/* Scrollbar مخصص جميل */
.reports-content::-webkit-scrollbar {
  width: 8px;
}

.reports-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 8px 0;
}

.reports-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(37, 99, 235, 0.8) 100%
  );
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.reports-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 1) 0%,
    rgba(37, 99, 235, 1) 100%
  );
  transform: scaleX(1.2);
}

.reports-container.dark .reports-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.reports-container.dark .reports-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(96, 165, 250, 0.8) 0%,
    rgba(59, 130, 246, 0.8) 100%
  );
}

/* منطقة إدخال بيانات التقرير محسنة */
.reports-input-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.reports-input-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    #3b82f6 0%,
    #8b5cf6 50%,
    #06b6d4 100%
  );
  border-radius: 20px 20px 0 0;
}

.reports-container.dark .reports-input-section {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.reports-input-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reports-container.dark .reports-input-title {
  color: #f9fafb;
}

/* نموذج إدخال البيانات */
.reports-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.reports-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reports-form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.reports-container.dark .reports-form-label {
  color: #d1d5db;
}

.reports-form-input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  background-color: white;
  color: #111827;
  transition: all 0.3s ease;
}

.reports-form-input:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.reports-container.dark .reports-form-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.reports-container.dark .reports-form-input:focus {
  border-color: #a78bfa;
  box-shadow: 0 0 0 3px rgba(167, 139, 250, 0.1);
}

/* زر توليد التقرير */
.reports-generate-btn {
  grid-column: 1 / -1;
  padding: 14px 20px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reports-generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.reports-generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}



.reports-generate-btn.loading {
  opacity: 0.8;
  cursor: wait;
}

/* منطقة عرض التقرير محسنة */
.reports-display-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  max-height: calc(100vh - 300px);
  position: relative;
}

.reports-display-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    #06b6d4 0%,
    #8b5cf6 50%,
    #3b82f6 100%
  );
  border-radius: 20px 20px 0 0;
}

.reports-container.dark .reports-display-section {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(75, 85, 99, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.reports-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.reports-container.dark .reports-display-header {
  border-bottom-color: #374151;
}

.reports-display-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.reports-container.dark .reports-display-title {
  color: #f9fafb;
}

.reports-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.reports-action-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.reports-action-btn:hover {
  border-color: #8b5cf6;
  background: #f5f3ff;
  color: #7c3aed;
}

.reports-container.dark .reports-action-btn {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.reports-container.dark .reports-action-btn:hover {
  border-color: #a78bfa;
  background: #581c87;
  color: #a78bfa;
}

/* محتوى التقرير محسن */
.reports-content-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  margin-top: 1rem;
  border-radius: 16px;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.8) 0%,
    rgba(241, 245, 249, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(5px);
  scroll-behavior: smooth;
  min-height: 300px;
  max-height: calc(100vh - 450px);
}

.reports-container.dark .reports-content-area {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.8) 0%,
    rgba(30, 41, 59, 0.8) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

/* Scrollbar محسن لمنطقة المحتوى */
.reports-content-area::-webkit-scrollbar {
  width: 10px;
}

.reports-content-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 8px 0;
}

.reports-content-area::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(139, 92, 246, 0.6) 0%,
    rgba(124, 58, 237, 0.6) 100%
  );
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.reports-content-area::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(139, 92, 246, 0.9) 0%,
    rgba(124, 58, 237, 0.9) 100%
  );
  transform: scaleX(1.3);
}

.reports-container.dark .reports-content-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.reports-container.dark .reports-content-area::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(168, 139, 250, 0.6) 0%,
    rgba(139, 92, 246, 0.6) 100%
  );
}

.reports-text {
  font-size: 15px;
  line-height: 1.9;
  color: #1f2937;
  white-space: pre-wrap;
  font-family: 'Georgia', 'Times New Roman', serif;
  text-align: justify;
  letter-spacing: 0.3px;
  word-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.reports-container.dark .reports-text {
  color: #e5e7eb;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* تحسين العناوين في النص */
.reports-text h1,
.reports-text h2,
.reports-text h3 {
  color: #1e40af;
  margin: 1.5rem 0 1rem 0;
  font-weight: 600;
}

.reports-container.dark .reports-text h1,
.reports-container.dark .reports-text h2,
.reports-container.dark .reports-text h3 {
  color: #60a5fa;
}

.reports-text p {
  margin-bottom: 1rem;
  text-indent: 1.5rem;
}

.reports-text ul,
.reports-text ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.reports-text li {
  margin-bottom: 0.5rem;
}

/* حالة فارغة */
.reports-empty {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.reports-container.dark .reports-empty {
  color: #9ca3af;
}

.reports-empty-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.reports-empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #111827;
}

.reports-container.dark .reports-empty-title {
  color: #f9fafb;
}

.reports-empty-subtitle {
  font-size: 13px;
  margin-bottom: 16px;
}

/* قوالب التقارير */
.reports-templates {
  margin-bottom: 16px;
}

.reports-templates-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
}

.reports-container.dark .reports-templates-title {
  color: #d1d5db;
}

.reports-templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
}

.reports-template-card {
  padding: 10px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.reports-template-card:hover {
  border-color: #8b5cf6;
  background: #f5f3ff;
  transform: translateY(-2px);
}

.reports-template-card.selected {
  border-color: #8b5cf6;
  background: #f5f3ff;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.reports-container.dark .reports-template-card {
  background: #374151;
  border-color: #4b5563;
}

.reports-container.dark .reports-template-card:hover,
.reports-container.dark .reports-template-card.selected {
  border-color: #a78bfa;
  background: #581c87;
}

.reports-template-name {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.reports-container.dark .reports-template-name {
  color: #f9fafb;
}

.reports-template-desc {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.3;
}

.reports-container.dark .reports-template-desc {
  color: #9ca3af;
}

/* إعدادات متقدمة */
.reports-advanced-settings {
  grid-column: 1 / -1;
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 12px;
}

.reports-container.dark .reports-advanced-settings {
  border-top-color: #374151;
}

.reports-advanced-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.reports-container.dark .reports-advanced-title {
  color: #d1d5db;
}

.reports-advanced-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

/* شريط التقدم */
.reports-progress-container {
  margin: 12px 0;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.reports-container.dark .reports-progress-container {
  background: #1e293b;
}

.reports-progress-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.reports-container.dark .reports-progress-title {
  color: #d1d5db;
}

.reports-progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.reports-container.dark .reports-progress-bar {
  background: #475569;
}

.reports-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6 0%, #a78bfa 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* إحصائيات التقرير محسنة */
.reports-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.reports-stat-item {
  flex: 1;
  min-width: 120px;
  padding: 1rem;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(241, 245, 249, 0.9) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  text-align: center;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.reports-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    #3b82f6 0%,
    #8b5cf6 50%,
    #06b6d4 100%
  );
}

.reports-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
}

.reports-container.dark .reports-stat-item {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.9) 100%
  );
  border-color: rgba(75, 85, 99, 0.6);
}

.reports-stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.reports-container.dark .reports-stat-number {
  color: #60a5fa;
}

.reports-stat-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.reports-container.dark .reports-stat-label {
  color: #94a3b8;
}

/* تأثيرات الكتابة */
.reports-typing-effect {
  overflow: hidden;
  border-right: 2px solid #8b5cf6;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #8b5cf6; }
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
  .reports-form {
    grid-template-columns: 1fr;
  }

  .reports-actions {
    flex-direction: column;
  }

  .reports-templates-grid {
    grid-template-columns: 1fr;
  }

  .reports-stats {
    flex-direction: column;
  }
}

/* حالة فارغة محسنة */
.reports-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.reports-container.dark .reports-empty {
  color: #9ca3af;
}

.reports-empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #8b5cf6 50%,
    #06b6d4 100%
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
  position: relative;
}

.reports-empty-icon::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #8b5cf6 50%,
    #06b6d4 100%
  );
  border-radius: 50%;
  z-index: -1;
  opacity: 0.3;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.reports-empty-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.reports-container.dark .reports-empty-title {
  color: #f9fafb;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.reports-empty-subtitle {
  font-size: 15px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
  max-width: 400px;
}

.reports-container.dark .reports-empty-subtitle {
  color: #9ca3af;
}
