/**
 * اختبار نظام الفهم العميق وتوليد الأسئلة الذكية
 */

const aiService = require('./src/services/aiService');

async function testDeepUnderstanding() {
  console.log('🧠 اختبار نظام الفهم العميق وتوليد الأسئلة الذكية...\n');

  try {
    // محتوى تجريبي متقدم في البرمجة
    const programmingContent = `
# خوارزميات الترتيب في علوم الحاسوب

## مقدمة
خوارزميات الترتيب هي خوارزميات أساسية في علوم الحاسوب تستخدم لترتيب عناصر مجموعة بيانات حسب ترتيب معين.

## خوارزمية الفقاعات (Bubble Sort)
خوارزمية الفقاعات تعمل بمقارنة العناصر المتجاورة وتبديلها إذا كانت في الترتيب الخاطئ.

### التعقيد الزمني:
- أفضل حالة: O(n) عندما تكون البيانات مرتبة مسبقاً
- أسوأ حالة: O(n²) عندما تكون البيانات مرتبة عكسياً
- المتوسط: O(n²)

### المزايا والعيوب:
**المزايا:**
- بسيطة في التنفيذ
- لا تحتاج ذاكرة إضافية (in-place)
- مستقرة (stable)

**العيوب:**
- بطيئة للمجموعات الكبيرة
- تعقيد زمني عالي O(n²)

## خوارزمية الدمج (Merge Sort)
خوارزمية الدمج تستخدم مبدأ "فرق تسد" حيث تقسم المصفوفة إلى نصفين، ترتب كل نصف، ثم تدمجهما.

### التعقيد الزمني:
- جميع الحالات: O(n log n)

### المزايا والعيوب:
**المزايا:**
- تعقيد زمني ثابت O(n log n)
- مستقرة
- تعمل بشكل جيد مع البيانات الكبيرة

**العيوب:**
- تحتاج ذاكرة إضافية O(n)
- أبطأ من Quick Sort في الممارسة العملية

## المقارنة والاختيار
اختيار خوارزمية الترتيب يعتمد على:
- حجم البيانات
- طبيعة البيانات (مرتبة جزئياً أم لا)
- قيود الذاكرة
- الحاجة للاستقرار

للبيانات الصغيرة: Insertion Sort
للبيانات الكبيرة: Merge Sort أو Quick Sort
للذاكرة المحدودة: Heap Sort
    `.trim();

    console.log('📖 المحتوى التجريبي:', programmingContent.substring(0, 200) + '...');
    console.log('📊 طول المحتوى:', programmingContent.length, 'حرف');

    // الخطوة 1: اختبار الفهم العميق
    console.log('\n🔍 الخطوة 1: اختبار الفهم العميق للمحتوى...');
    const understandingResult = await aiService.deepContentUnderstanding(programmingContent, {
      language: 'ar'
    });

    if (understandingResult.success) {
      console.log('✅ نجح الفهم العميق للمحتوى');
      console.log('📊 ملخص الفهم:', understandingResult.understanding.summary);
      console.log('🎯 الموضوع الرئيسي:', understandingResult.understanding.mainTopic);
      console.log('📝 عدد المفاهيم:', understandingResult.understanding.concepts?.length || 0);
      console.log('🔬 عدد النقاط القابلة للاختبار:', understandingResult.understanding.testablePoints?.length || 0);

      // عرض المفاهيم المستخرجة
      if (understandingResult.understanding.concepts) {
        console.log('\n📚 المفاهيم المستخرجة:');
        understandingResult.understanding.concepts.forEach((concept, index) => {
          console.log(`${index + 1}. ${concept.name} (${concept.importance})`);
          console.log(`   التعريف: ${concept.definition}`);
        });
      }

      // عرض النقاط القابلة للاختبار
      if (understandingResult.understanding.testablePoints) {
        console.log('\n🎯 النقاط القابلة للاختبار:');
        understandingResult.understanding.testablePoints.forEach((point, index) => {
          console.log(`${index + 1}. ${point.point} (${point.type} - ${point.difficulty})`);
        });
      }

      // الخطوة 2: اختبار توليد الأسئلة من الفهم
      console.log('\n🧠 الخطوة 2: توليد أسئلة ذكية بناءً على الفهم...');
      const questionsResult = await aiService.generateQuestionsFromUnderstanding(
        understandingResult.understanding,
        {
          questionCount: 4,
          difficulty: 'medium',
          language: 'ar'
        }
      );

      if (questionsResult.success) {
        console.log('✅ نجح توليد الأسئلة الذكية');
        
        try {
          const questions = JSON.parse(questionsResult.data);
          
          console.log('\n📋 الأسئلة الذكية المولدة:');
          questions.questions?.forEach((q, index) => {
            console.log(`\n${index + 1}. ${q.questionText}`);
            console.log(`   المستوى المعرفي: ${q.cognitiveLevel}`);
            console.log(`   مبني على المفهوم: ${q.basedOnConcept}`);
            console.log(`   الخيارات:`);
            q.options?.forEach((option, optIndex) => {
              const marker = optIndex === q.correctAnswer ? '✓' : ' ';
              console.log(`   ${marker} ${String.fromCharCode(65 + optIndex)}. ${option}`);
            });
            console.log(`   الشرح: ${q.explanation}`);
          });

          if (questions.metadata) {
            console.log('\n📊 معلومات الأسئلة:');
            console.log('   - مبنية على الفهم العميق:', questions.metadata.basedOnUnderstanding);
            console.log('   - الموضوع الرئيسي:', questions.metadata.mainTopic);
            console.log('   - نوع التوليد:', questions.metadata.generationType);
          }

        } catch (parseError) {
          console.log('❌ خطأ في تحليل JSON للأسئلة:', parseError.message);
        }
      } else {
        console.log('❌ فشل توليد الأسئلة من الفهم');
      }

    } else {
      console.log('❌ فشل الفهم العميق للمحتوى:', understandingResult.error);
    }

    // الخطوة 3: اختبار النظام الكامل
    console.log('\n🚀 الخطوة 3: اختبار النظام الكامل...');
    const fullSystemResult = await aiService.generateIntelligentQuestions(programmingContent, {}, {
      questionCount: 3,
      difficulty: 'medium',
      language: 'ar'
    });

    if (fullSystemResult.success) {
      console.log('✅ نجح النظام الكامل');
      
      try {
        const fullQuestions = JSON.parse(fullSystemResult.data);
        
        console.log('\n🎯 أسئلة النظام الكامل:');
        fullQuestions.questions?.forEach((q, index) => {
          console.log(`${index + 1}. ${q.questionText}`);
        });

      } catch (parseError) {
        console.log('⚠️ استخدام النظام البديل في النظام الكامل');
      }
    } else {
      console.log('❌ فشل النظام الكامل');
    }

    console.log('\n✅ انتهى اختبار نظام الفهم العميق!');

  } catch (error) {
    console.error('💥 خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testDeepUnderstanding();
}

module.exports = { testDeepUnderstanding };
