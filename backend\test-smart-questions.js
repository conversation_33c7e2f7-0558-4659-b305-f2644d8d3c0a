/**
 * اختبار نظام الأسئلة الذكية المحسن
 */

const aiService = require('./src/services/aiService');

async function testSmartQuestions() {
  console.log('🧪 اختبار نظام الأسئلة الذكية المحسن...\n');

  try {
    // محتوى تجريبي باللغة العربية في البرمجة
    const arabicContent = `
# البرمجة الكائنية في JavaScript

## مقدمة
البرمجة الكائنية هي نموذج برمجي يعتمد على مفهوم الكائنات التي تحتوي على خصائص ووظائف.

## الفئات والكائنات
الفئة (Class) هي قالب لإنشاء الكائنات:

\`\`\`javascript
class Car {
  constructor(brand, model) {
    this.brand = brand;
    this.model = model;
  }
  
  start() {
    console.log("السيارة تعمل");
  }
}

const myCar = new Car("Toyota", "Camry");
\`\`\`

## الوراثة
الوراثة تسمح لفئة بوراثة خصائص من فئة أخرى:

\`\`\`javascript
class ElectricCar extends Car {
  constructor(brand, model, batteryLife) {
    super(brand, model);
    this.batteryLife = batteryLife;
  }
  
  charge() {
    console.log("جاري شحن البطارية");
  }
}
\`\`\`

## الفوائد
- إعادة استخدام الكود
- سهولة الصيانة
- تنظيم أفضل للكود
    `.trim();

    // محتوى تجريبي باللغة الإنجليزية في العلوم
    const englishContent = `
# Photosynthesis Process

## Introduction
Photosynthesis is the process by which plants convert light energy into chemical energy.

## The Process
The photosynthesis equation is:
6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂

## Key Components
1. **Chlorophyll**: The green pigment that captures light energy
2. **Stomata**: Pores that allow gas exchange
3. **Chloroplasts**: Organelles where photosynthesis occurs

## Light-Dependent Reactions
These reactions occur in the thylakoids and produce:
- ATP (adenosine triphosphate)
- NADPH (nicotinamide adenine dinucleotide phosphate)
- Oxygen as a byproduct

## Light-Independent Reactions (Calvin Cycle)
These reactions use ATP and NADPH to convert CO₂ into glucose.

## Importance
Photosynthesis is crucial for:
- Oxygen production
- Food chain foundation
- Carbon dioxide removal from atmosphere
    `.trim();

    console.log('🔍 اختبار المحتوى العربي...');
    console.log('📊 طول المحتوى:', arabicContent.length, 'حرف');
    
    // اختبار كشف اللغة
    const arabicLanguage = aiService.detectContentLanguage(arabicContent);
    console.log('🌐 لغة المحتوى العربي المكتشفة:', arabicLanguage);

    // اختبار توليد أسئلة ذكية للمحتوى العربي
    const arabicQuestions = await aiService.generateIntelligentQuestions(
      arabicContent, 
      {
        classification: {
          mainTopic: 'البرمجة الكائنية',
          academicField: 'علوم الحاسوب',
          difficulty: 'intermediate'
        }
      },
      {
        questionCount: 3,
        difficulty: 'medium',
        language: 'ar'
      }
    );

    if (arabicQuestions.success) {
      console.log('✅ نجح توليد الأسئلة العربية');
      try {
        const questions = JSON.parse(arabicQuestions.data);
        console.log('\n📋 الأسئلة العربية المولدة:');
        questions.questions?.forEach((q, index) => {
          console.log(`${index + 1}. ${q.questionText}`);
          console.log(`   الخيارات: ${q.options?.length || 0} خيارات`);
        });
      } catch (e) {
        console.log('⚠️ استخدام الأسئلة البديلة للمحتوى العربي');
      }
    }

    console.log('\n🔍 اختبار المحتوى الإنجليزي...');
    console.log('📊 طول المحتوى:', englishContent.length, 'حرف');
    
    // اختبار كشف اللغة
    const englishLanguage = aiService.detectContentLanguage(englishContent);
    console.log('🌐 لغة المحتوى الإنجليزي المكتشفة:', englishLanguage);

    // اختبار توليد أسئلة ذكية للمحتوى الإنجليزي
    const englishQuestions = await aiService.generateIntelligentQuestions(
      englishContent, 
      {
        classification: {
          mainTopic: 'Photosynthesis',
          academicField: 'Biology',
          difficulty: 'intermediate'
        }
      },
      {
        questionCount: 3,
        difficulty: 'medium',
        language: 'en'
      }
    );

    if (englishQuestions.success) {
      console.log('✅ نجح توليد الأسئلة الإنجليزية');
      try {
        const questions = JSON.parse(englishQuestions.data);
        console.log('\n📋 الأسئلة الإنجليزية المولدة:');
        questions.questions?.forEach((q, index) => {
          console.log(`${index + 1}. ${q.questionText}`);
          console.log(`   Options: ${q.options?.length || 0} options`);
        });
      } catch (e) {
        console.log('⚠️ استخدام الأسئلة البديلة للمحتوى الإنجليزي');
      }
    }

    console.log('\n✅ انتهى اختبار النظام المحسن!');

  } catch (error) {
    console.error('💥 خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testSmartQuestions();
}

module.exports = { testSmartQuestions };
