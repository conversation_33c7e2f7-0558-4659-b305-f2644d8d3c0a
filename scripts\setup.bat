@echo off
echo 🎓 AI Educational Platform - Setup Script (Windows)
echo ===================================================

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo Please install Node.js 18+ from https://nodejs.org/
    echo After installation, run this script again.
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
)

:: Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    echo npm should be installed with Node.js
    pause
    exit /b 1
) else (
    echo ✅ npm found
)

:: Install root dependencies
echo.
echo 📦 Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)
echo ✅ Root dependencies installed

:: Setup environment files
echo.
echo ⚙️ Setting up environment files...
if not exist ".env" (
    copy ".env.example" ".env" >nul
    echo ✅ Created .env file from template
    echo ⚠️ Please update .env with your actual configuration
) else (
    echo ✅ .env file already exists
)

:: Create necessary directories
echo.
echo 📁 Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\uploads" mkdir backend\uploads
if not exist "database\backups" mkdir database\backups
if not exist "nginx\logs" mkdir nginx\logs
if not exist "docker\data" mkdir docker\data
echo ✅ Directories created

:: Final instructions
echo.
echo 🎉 Setup completed successfully!
echo.
echo Next steps:
echo 1. Update configuration files:
echo    - .env (main configuration)
echo    - frontend\.env (frontend configuration)
echo    - backend\.env (backend configuration)
echo.
echo 2. Install service dependencies:
echo    npm run install:all
echo.
echo 3. Start development servers:
echo    npm run dev
echo.
echo 4. Access the application:
echo    Frontend:  http://localhost:3000
echo    Backend:   http://localhost:8000
echo    API Docs:  http://localhost:8000/docs
echo    Admin:     http://localhost:3001
echo.
echo 📖 For detailed instructions, see README.md
echo.
pause
