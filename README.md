# 🎓 منصة التعليم الذكية | AI Educational Platform

<div align="center">

![Platform Logo](https://img.shields.io/badge/AI-Educational%20Platform-blue?style=for-the-badge&logo=graduation-cap)
![Version](https://img.shields.io/badge/version-1.0.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/license-MIT-yellow?style=for-the-badge)

**منصة تعليمية متكاملة تستخدم الذكاء الاصطناعي لتوفير تجربة تعليمية متقدمة**

[🚀 البدء السريع](#-البدء-السريع) • [📖 الميزات](#-الميزات) • [🛠️ التطوير](#️-التطوير) • [🔒 الأمان](#-الأمان)

</div>

---

## 🎯 نظرة عامة

منصة التعليم الذكية هي نظام تعليمي شامل يستخدم أحدث تقنيات الذكاء الاصطناعي لتوفير تجربة تعليمية متقدمة ومخصصة. تدعم المنصة اللغتين العربية والإنجليزية مع واجهة مستخدم حديثة ومتجاوبة.

### 🎯 الأهداف الرئيسية

- **التعلم التفاعلي**: توفير بيئة تعليمية تفاعلية وجذابة
- **الذكاء الاصطناعي**: استخدام AI لتحسين التعلم وتخصيص المحتوى
- **سهولة الاستخدام**: واجهة بديهية تدعم جميع الأجهزة
- **الأمان**: حماية شاملة للبيانات والخصوصية
- **التحليلات**: مراقبة الأداء وتقديم التوصيات

---

## ✨ الميزات

### 🎓 **قسم المحاضرات**
- **محادثات ذكية**: نظام دردشة تفاعلي مع AI
- **فهم الملفات**: تحليل وشرح المستندات المرفوعة
- **دعم متعدد الصيغ**: PDF, DOCX, TXT, وأكثر
- **ذاكرة المحادثة**: حفظ واستكمال المحادثات السابقة
- **استجابات مخصصة**: إجابات متخصصة حسب المحتوى

### ❓ **قسم الأسئلة**
- **توليد ذكي**: إنشاء أسئلة متنوعة من المحتوى
- **مستويات صعوبة**: سهل، متوسط، صعب، ومختلط
- **أسئلة متعددة الخيارات**: مع إجابات وتفسيرات
- **فهم عميق**: تحليل المحتوى وليس فقط المعلومات السطحية
- **حد يومي**: 5 استخدامات يومية مع Google API

### 💻 **قسم المهام**
- **مهام برمجية**: أكثر من 100 مهمة لكل لغة
- **مستويات متدرجة**: من المبتدئ إلى المتقدم
- **تصحيح تلقائي**: فحص الحلول باستخدام AI
- **نظام نقاط**: تتبع التقدم والإنجازات
- **ملاحظات ذكية**: اقتراحات لتحسين الكود

### 📊 **قسم التقارير**
- **تقارير أكاديمية**: مستوى جامعي عالي الجودة
- **مصادر موثقة**: مراجع ومصادر علمية
- **تخصيص الطول**: من 3-15 صفحة
- **دعم اللغات**: عربي وإنجليزي
- **تنسيق احترافي**: تصميم أكاديمي متقن

### 📈 **قسم التحليلات** (الميزة الرئيسية)
- **تحليل الأداء**: مراقبة شاملة لتقدم الطلاب
- **ذكاء اصطناعي متقدم**: استخدام جميع نماذج AI المجانية
- **توصيات مخصصة**: اقتراحات لتحسين الأداء
- **تتبع الاستخدام**: إحصائيات مفصلة لجميع الأقسام
- **تقارير دورية**: تحليلات أسبوعية وشهرية

### 🆘 **قسم الدعم**
- **معلومات الفريق**: تفاصيل المطورين والمشرفين
- **وسائل التواصل**: روابط مباشرة للدعم
- **أهداف المنصة**: رؤية ورسالة المشروع
- **تأثيرات تفاعلية**: تصميم جذاب ومتحرك

---

## 🏗️ البنية التقنية

### 🎨 **Frontend**
```
React 18 + TypeScript + Vite
├── 🎨 Tailwind CSS - للتصميم
├── 🎭 Framer Motion - للحركات والتأثيرات
├── 🧭 React Router - للتنقل
├── 🌐 Zustand - لإدارة الحالة
├── 📱 تصميم متجاوب - دعم كامل للجوال
└── 🌙 الوضع المظلم - تبديل سلس بين الأوضاع
```

### ⚙️ **Backend**
```
Node.js + Express.js
├── 🔐 JWT Authentication - مصادقة آمنة
├── 🛡️ Security Middleware - حماية شاملة
├── 📊 MongoDB + Mongoose - قاعدة بيانات
├── 📁 Multer - رفع الملفات
├── 🤖 OpenRouter API - خدمات AI
└── 📝 Express Validator - التحقق من البيانات
```

### 🗄️ **قاعدة البيانات**
```
MongoDB Atlas
├── 👥 Users Collection - بيانات المستخدمين
├── 💬 Conversations - محادثات المحاضرات
├── ❓ Questions - الأسئلة والنتائج
├── 💻 Tasks - المهام والحلول
├── 📊 Analytics - بيانات التحليلات
└── 🔒 Sessions - جلسات المستخدمين
```

### 🤖 **خدمات الذكاء الاصطناعي**
```
OpenRouter API (نماذج مجانية)
├── 🧠 DeepSeek - للمهام البرمجية
├── 📚 Claude - للمحاضرات والتقارير
├── 🔍 GPT-3.5 - للتحليلات
└── 🎯 Google API - لتوليد الأسئلة
```

---

## 🚀 البدء السريع

### 📋 المتطلبات الأساسية

- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **MongoDB Atlas** (حساب مجاني)
- **OpenRouter API Key** (مجاني)
- **Google API Key** (مجاني)

### ⚡ التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/ai-educational-platform.git
cd ai-educational-platform

# 2. تثبيت التبعيات
npm run install:all

# 3. إعداد متغيرات البيئة
cp backend/.env.example backend/.env
# قم بتحرير backend/.env وإضافة مفاتيح API

# 4. تشغيل المشروع
npm run dev
```

### 🔧 إعداد متغيرات البيئة

إنشئ ملف `backend/.env`:

```env
# قاعدة البيانات
MONGODB_URI=mongodb+srv://username:<EMAIL>/education_platform

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# OpenRouter API
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here

# Google API
GOOGLE_API_KEY=your-google-api-key-here

# إعدادات الخادم
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# الأمان
SESSION_SECRET=your-session-secret-here
CSRF_SECRET=your-csrf-secret-here
```

### 🌐 الوصول للمنصة

بعد التشغيل، ستكون المنصة متاحة على:

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/health

---

## 🛠️ التطوير

### 📦 الأوامر المتاحة

```bash
# تطوير
npm run dev              # تشغيل Frontend + Backend
npm run dev:frontend     # تشغيل Frontend فقط
npm run dev:backend      # تشغيل Backend فقط

# بناء المشروع
npm run build            # بناء Frontend + Backend
npm run build:frontend   # بناء Frontend فقط
npm run build:backend    # بناء Backend فقط

# اختبار
npm run test             # تشغيل جميع الاختبارات
npm run test:frontend    # اختبار Frontend
npm run test:backend     # اختبار Backend

# فحص الكود
npm run lint             # فحص جميع الملفات
npm run lint:frontend    # فحص Frontend
npm run lint:backend     # فحص Backend

# صحة النظام
npm run health           # فحص حالة الخوادم
```

### 🏗️ هيكل المشروع

```
ai-educational-platform/
├── 📁 frontend/              # تطبيق React
│   ├── 📁 src/
│   │   ├── 📁 components/    # مكونات React
│   │   ├── 📁 contexts/      # إدارة الحالة
│   │   ├── 📁 hooks/         # Custom Hooks
│   │   ├── 📁 utils/         # وظائف مساعدة
│   │   └── 📁 styles/        # ملفات CSS
│   └── 📄 package.json
├── 📁 backend/               # خادم Node.js
│   ├── 📁 src/
│   │   ├── 📁 routes/        # مسارات API
│   │   ├── 📁 models/        # نماذج قاعدة البيانات
│   │   ├── 📁 middleware/    # وسطاء Express
│   │   ├── 📁 controllers/   # منطق التحكم
│   │   └── 📁 utils/         # وظائف مساعدة
│   └── 📄 package.json
├── 📁 uploads/               # ملفات مرفوعة
└── 📄 package.json           # إعدادات المشروع الرئيسية
```

---

## 📱 دعم الجوال

المنصة مصممة لتكون متجاوبة بالكامل مع جميع الأجهزة:

### 📱 **الميزات المحسنة للجوال**
- **تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- **قائمة جانبية منزلقة**: تفاعل سلس على الجوال
- **أهداف لمس محسنة**: أزرار بحجم مناسب للمس
- **تحسينات الأداء**: تحميل سريع على الشبكات البطيئة
- **دعم الإيماءات**: تفاعل طبيعي مع اللمس

### 📐 **نقاط التوقف المدعومة**
- **الهواتف الصغيرة**: ≤ 480px
- **الهواتف العادية**: ≤ 768px  
- **الأجهزة اللوحية**: 769px - 1024px
- **أجهزة سطح المكتب**: > 1024px

---

## 🔒 الأمان

المنصة تطبق أعلى معايير الأمان:

### 🛡️ **طبقات الحماية**
- **مصادقة JWT**: رموز آمنة مع انتهاء صلاحية
- **تشفير كلمات المرور**: bcrypt مع salt قوي
- **حماية CSRF**: رموز مكافحة التزوير
- **Rate Limiting**: حماية من الهجمات المتكررة
- **تنظيف المدخلات**: منع حقن SQL/NoSQL
- **حماية XSS**: تنظيف المحتوى من البرمجيات الخبيثة
- **Headers أمنية**: Helmet.js للحماية الشاملة

### 🔐 **مراقبة الأمان**
- **تسجيل الأحداث**: مراقبة جميع الأنشطة المشبوهة
- **تنبيهات فورية**: إشعارات عند اكتشاف تهديدات
- **تحليل الأنماط**: كشف السلوك غير الطبيعي
- **نسخ احتياطية آمنة**: حماية البيانات من الفقدان

---

## 👥 الفريق

### 👨‍💻 **المطور الرئيسي**
- **أحمد مشتاق Ǎļ Çandarli**

### 👨‍🏫 **المشرف الأكاديمي**
- **د.ا.م محسن حسن**

### 👥 **المساعدون**
- **Fenix** 
- **فهد**

### 🙏 **شكر خاص**
- **محمد أحمد** 
- **مهيمن حسن** 

---

## 📞 التواصل والدعم

### 🌐 **وسائل التواصل**
- **Instagram**: [@platform_instagram](https://instagram.com/platform_instagram)
- **YouTube**: [قناة المنصة](https://youtube.com/@platform_channel)
- **X (Twitter)**: [@platform_x](https://x.com/platform_x)
- **GitHub**: [المشروع على GitHub](https://github.com/your-username/ai-educational-platform)
- **Telegram**: [@platform_support](https://t.me/platform_support)

---

<div align="center">

**🎓 منصة التعليم الذكية - تعلم بذكاء، انجز بتميز**

Made with ❤️ by the AI Educational Platform Team

![Status](https://img.shields.io/badge/Status-Active-brightgreen)
![Maintenance](https://img.shields.io/badge/Maintained-Yes-green)

</div>
