/**
 * خدمة إدارة التخزين المحلي والحالة
 * Local Storage and State Management Service
 */

export interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
  sidebarCollapsed: boolean;
  lastActiveSection: string;
}

export interface UserSession {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    level: string;
    stats: {
      totalPoints: number;
      completedTasks: number;
      answeredQuestions: number;
    };
  };
  loginTime: string;
  lastActivity: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  section: string;
  metadata?: any;
}

export interface UserData {
  lectures: any[];
  questions: any[];
  tasks: any[];
  reports: any[];
  analytics: any[];
  chats: ChatMessage[];
  preferences: UserPreferences;
  session: UserSession | null;
}

class StorageService {
  private readonly STORAGE_KEYS = {
    USER_SESSION: 'ai_edu_user_session',
    USER_PREFERENCES: 'ai_edu_user_preferences',
    USER_DATA: 'ai_edu_user_data',
    CHAT_MESSAGES: 'ai_edu_chat_messages',
    LAST_SYNC: 'ai_edu_last_sync'
  };

  private readonly CHAT_RETENTION_DAYS = 14; // أسبوعين

  /**
   * حفظ جلسة المستخدم
   */
  saveUserSession(session: UserSession): void {
    try {
      localStorage.setItem(this.STORAGE_KEYS.USER_SESSION, JSON.stringify(session));
      this.updateLastActivity();
    } catch (error) {
      console.error('خطأ في حفظ جلسة المستخدم:', error);
    }
  }

  /**
   * الحصول على جلسة المستخدم
   */
  getUserSession(): UserSession | null {
    try {
      const session = localStorage.getItem(this.STORAGE_KEYS.USER_SESSION);
      if (session) {
        const parsedSession = JSON.parse(session);
        // التحقق من انتهاء صلاحية الجلسة (7 أيام)
        const loginTime = new Date(parsedSession.loginTime);
        const now = new Date();
        const daysDiff = (now.getTime() - loginTime.getTime()) / (1000 * 3600 * 24);
        
        if (daysDiff > 7) {
          this.clearUserSession();
          return null;
        }
        
        return parsedSession;
      }
      return null;
    } catch (error) {
      console.error('خطأ في الحصول على جلسة المستخدم:', error);
      return null;
    }
  }

  /**
   * مسح جلسة المستخدم
   */
  clearUserSession(): void {
    localStorage.removeItem(this.STORAGE_KEYS.USER_SESSION);
  }

  /**
   * حفظ تفضيلات المستخدم
   */
  saveUserPreferences(preferences: UserPreferences): void {
    try {
      localStorage.setItem(this.STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
      this.syncPreferencesToServer(preferences);
    } catch (error) {
      console.error('خطأ في حفظ التفضيلات:', error);
    }
  }

  /**
   * الحصول على تفضيلات المستخدم
   */
  getUserPreferences(): UserPreferences {
    try {
      const preferences = localStorage.getItem(this.STORAGE_KEYS.USER_PREFERENCES);
      if (preferences) {
        return JSON.parse(preferences);
      }
    } catch (error) {
      console.error('خطأ في الحصول على التفضيلات:', error);
    }

    // التفضيلات الافتراضية
    return {
      theme: 'light',
      language: 'ar',
      sidebarCollapsed: false,
      lastActiveSection: 'lectures'
    };
  }

  /**
   * حفظ رسالة محادثة
   */
  saveChatMessage(message: ChatMessage): void {
    try {
      const messages = this.getChatMessages();
      messages.push(message);
      
      // تنظيف الرسائل القديمة
      const cleanedMessages = this.cleanOldMessages(messages);
      
      localStorage.setItem(this.STORAGE_KEYS.CHAT_MESSAGES, JSON.stringify(cleanedMessages));
      this.syncChatToServer(message);
    } catch (error) {
      console.error('خطأ في حفظ رسالة المحادثة:', error);
    }
  }

  /**
   * الحصول على رسائل المحادثة
   */
  getChatMessages(section?: string): ChatMessage[] {
    try {
      const messages = localStorage.getItem(this.STORAGE_KEYS.CHAT_MESSAGES);
      if (messages) {
        const parsedMessages = JSON.parse(messages);
        const cleanedMessages = this.cleanOldMessages(parsedMessages);
        
        if (section) {
          return cleanedMessages.filter(msg => msg.section === section);
        }
        
        return cleanedMessages;
      }
      return [];
    } catch (error) {
      console.error('خطأ في الحصول على رسائل المحادثة:', error);
      return [];
    }
  }

  /**
   * تنظيف الرسائل القديمة (أكثر من أسبوعين)
   */
  private cleanOldMessages(messages: ChatMessage[]): ChatMessage[] {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.CHAT_RETENTION_DAYS);
    
    return messages.filter(message => {
      const messageDate = new Date(message.timestamp);
      return messageDate > cutoffDate;
    });
  }

  /**
   * حفظ بيانات المستخدم
   */
  saveUserData(key: keyof UserData, data: any): void {
    try {
      const userData = this.getUserData();
      userData[key] = data;
      localStorage.setItem(this.STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      this.syncDataToServer(key, data);
    } catch (error) {
      console.error('خطأ في حفظ بيانات المستخدم:', error);
    }
  }

  /**
   * الحصول على بيانات المستخدم
   */
  getUserData(): UserData {
    try {
      const data = localStorage.getItem(this.STORAGE_KEYS.USER_DATA);
      if (data) {
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('خطأ في الحصول على بيانات المستخدم:', error);
    }

    return {
      lectures: [],
      questions: [],
      tasks: [],
      reports: [],
      analytics: [],
      chats: [],
      preferences: this.getUserPreferences(),
      session: null
    };
  }

  /**
   * تحديث آخر نشاط
   */
  updateLastActivity(): void {
    const session = this.getUserSession();
    if (session) {
      session.lastActivity = new Date().toISOString();
      this.saveUserSession(session);
    }
  }

  /**
   * مزامنة التفضيلات مع الخادم
   */
  private async syncPreferencesToServer(preferences: UserPreferences): Promise<void> {
    const session = this.getUserSession();
    if (!session) return;

    try {
      await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.token}`
        },
        body: JSON.stringify({ preferences })
      });
    } catch (error) {
      console.error('خطأ في مزامنة التفضيلات:', error);
    }
  }

  /**
   * مزامنة رسالة المحادثة مع الخادم
   */
  private async syncChatToServer(message: ChatMessage): Promise<void> {
    const session = this.getUserSession();
    if (!session) return;

    try {
      await fetch('/api/analytics/activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.token}`
        },
        body: JSON.stringify({
          activityType: 'chatMessage',
          increment: 1,
          metadata: {
            section: message.section,
            messageType: message.type,
            timestamp: message.timestamp
          }
        })
      });
    } catch (error) {
      console.error('خطأ في مزامنة المحادثة:', error);
    }
  }

  /**
   * مزامنة البيانات مع الخادم
   */
  private async syncDataToServer(key: string, data: any): Promise<void> {
    const session = this.getUserSession();
    if (!session) return;

    try {
      await fetch('/api/analytics/activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.token}`
        },
        body: JSON.stringify({
          activityType: `${key}Updated`,
          increment: 1,
          metadata: {
            dataType: key,
            timestamp: new Date().toISOString()
          }
        })
      });
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
    }
  }

  /**
   * مزامنة جميع البيانات مع الخادم
   */
  async syncAllDataToServer(): Promise<void> {
    const session = this.getUserSession();
    if (!session) return;

    try {
      const userData = this.getUserData();
      const preferences = this.getUserPreferences();
      const chatMessages = this.getChatMessages();

      // مزامنة التفضيلات
      await this.syncPreferencesToServer(preferences);

      // مزامنة إحصائيات الاستخدام
      await fetch('/api/analytics/activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.token}`
        },
        body: JSON.stringify({
          activityType: 'dataSync',
          increment: 1,
          metadata: {
            lecturesCount: userData.lectures.length,
            questionsCount: userData.questions.length,
            tasksCount: userData.tasks.length,
            reportsCount: userData.reports.length,
            chatMessagesCount: chatMessages.length,
            syncTime: new Date().toISOString()
          }
        })
      });

      localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
    }
  }

  /**
   * تنظيف البيانات المحلية
   */
  clearAllData(): void {
    Object.values(this.STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  /**
   * الحصول على آخر وقت مزامنة
   */
  getLastSyncTime(): Date | null {
    try {
      const lastSync = localStorage.getItem(this.STORAGE_KEYS.LAST_SYNC);
      return lastSync ? new Date(lastSync) : null;
    } catch (error) {
      return null;
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const storageService = new StorageService();

// تصدير الأنواع
export default storageService;
