/**
 * اختبار شامل لجميع مكونات النظام
 * Complete System Test
 */

require('dotenv').config();
const databaseConfig = require('./src/config/database');
const aiService = require('./src/services/aiService');
const fileService = require('./src/services/fileService');
const { User, Lecture, Question, Task, Report, Analytics } = require('./src/models');

async function runCompleteTest() {
  console.log('🚀 بدء الاختبار الشامل للنظام...\n');
  
  let testResults = {
    database: false,
    ai: false,
    fileService: false,
    models: false,
    integration: false
  };

  try {
    // 1. اختبار قاعدة البيانات
    console.log('1️⃣ اختبار قاعدة البيانات...');
    try {
      await databaseConfig.connect();
      console.log('✅ الاتصال بقاعدة البيانات نجح');
      testResults.database = true;
    } catch (error) {
      console.log('❌ فشل الاتصال بقاعدة البيانات:', error.message);
      return testResults;
    }

    // 2. اختبار الذكاء الاصطناعي
    console.log('\n2️⃣ اختبار خدمة الذكاء الاصطناعي...');
    try {
      const aiTest = await aiService.testConnection();
      if (aiTest.success) {
        console.log('✅ خدمة الذكاء الاصطناعي تعمل بشكل صحيح');
        testResults.ai = true;
      } else {
        console.log('❌ فشل في اختبار الذكاء الاصطناعي:', aiTest.message);
      }
    } catch (error) {
      console.log('❌ خطأ في خدمة الذكاء الاصطناعي:', error.message);
    }

    // 3. اختبار خدمة الملفات
    console.log('\n3️⃣ اختبار خدمة الملفات...');
    try {
      await fileService.initializeUploadDirectory();
      const testFileInfo = fileService.formatFileSize(1024000);
      console.log(`✅ خدمة الملفات تعمل بشكل صحيح - مثال: ${testFileInfo}`);
      testResults.fileService = true;
    } catch (error) {
      console.log('❌ خطأ في خدمة الملفات:', error.message);
    }

    // 4. اختبار النماذج
    console.log('\n4️⃣ اختبار النماذج...');
    try {
      // إنشاء مستخدم تجريبي
      const testUser = new User({
        name: 'مستخدم تجريبي',
        email: `test_${Date.now()}@example.com`,
        password: '123456'
      });

      // حفظ المستخدم
      const savedUser = await testUser.save();
      console.log(`✅ تم إنشاء مستخدم تجريبي: ${savedUser.name}`);

      // إنشاء محاضرة تجريبية
      const testLecture = new Lecture({
        userId: savedUser._id,
        title: 'محاضرة تجريبية - JavaScript',
        content: 'محتوى المحاضرة التجريبية حول JavaScript',
        fileUrl: '/uploads/test.pdf',
        fileName: 'test.pdf',
        fileType: 'pdf',
        fileSize: 1024
      });

      const savedLecture = await testLecture.save();
      console.log(`✅ تم إنشاء محاضرة تجريبية: ${savedLecture.title}`);

      // إنشاء سؤال تجريبي
      const testQuestion = new Question({
        userId: savedUser._id,
        lectureId: savedLecture._id,
        questionText: 'ما هو JavaScript؟',
        questionType: 'multiple_choice',
        options: [
          { text: 'لغة برمجة', isCorrect: true },
          { text: 'قاعدة بيانات', isCorrect: false }
        ],
        correctAnswer: 'لغة برمجة'
      });

      const savedQuestion = await testQuestion.save();
      console.log(`✅ تم إنشاء سؤال تجريبي: ${savedQuestion.questionText}`);

      // إنشاء مهمة تجريبية
      const testTask = new Task({
        title: 'مهمة تجريبية - حساب المضروب',
        description: 'اكتب دالة لحساب مضروب العدد',
        category: 'algorithms',
        language: 'javascript',
        difficulty: 'beginner',
        testCases: [
          { input: '5', expectedOutput: '120' }
        ],
        solution: 'function factorial(n) { return n <= 1 ? 1 : n * factorial(n - 1); }'
      });

      const savedTask = await testTask.save();
      console.log(`✅ تم إنشاء مهمة تجريبية: ${savedTask.title}`);

      // إنشاء تحليلات تجريبية
      const testAnalytics = new Analytics({
        userId: savedUser._id,
        date: new Date()
      });

      const savedAnalytics = await testAnalytics.save();
      console.log(`✅ تم إنشاء تحليلات تجريبية للتاريخ: ${savedAnalytics.date.toDateString()}`);

      testResults.models = true;

      // 5. اختبار التكامل
      console.log('\n5️⃣ اختبار التكامل...');

      // اختبار إجابة السؤال
      const answerResult = savedQuestion.answerQuestion('لغة برمجة', 30);
      console.log(`✅ اختبار إجابة السؤال: ${answerResult ? 'صحيحة' : 'خاطئة'}`);

      // اختبار إضافة نقاط للمستخدم
      savedUser.addPoints(50);
      await savedUser.save();
      console.log(`✅ تم إضافة نقاط للمستخدم: ${savedUser.stats.totalPoints}`);

      // اختبار تحديث إحصائيات المحاضرة
      savedLecture.incrementViewCount();
      await savedLecture.save();
      console.log(`✅ تم تحديث عدد مشاهدات المحاضرة: ${savedLecture.stats.viewCount}`);

      // اختبار إضافة محاولة للمهمة
      savedTask.addSubmission(savedUser._id, 'function factorial(n) { return n <= 1 ? 1 : n * factorial(n - 1); }', 'passed', 100, 1500);
      await savedTask.save();
      console.log(`✅ تم إضافة محاولة للمهمة: ${savedTask.stats.totalAttempts} محاولة`);

      // اختبار تحديث التحليلات
      savedAnalytics.updateActivity('questionsAnswered', 1);
      savedAnalytics.updatePerformance(1, 1, 10);
      await savedAnalytics.save();
      console.log(`✅ تم تحديث التحليلات: ${savedAnalytics.activities.questionsAnswered} سؤال`);

      testResults.integration = true;

      // تنظيف البيانات التجريبية
      console.log('\n🧹 تنظيف البيانات التجريبية...');
      await Analytics.deleteOne({ _id: savedAnalytics._id });
      await Task.deleteOne({ _id: savedTask._id });
      await Question.deleteOne({ _id: savedQuestion._id });
      await Lecture.deleteOne({ _id: savedLecture._id });
      await User.deleteOne({ _id: savedUser._id });
      console.log('✅ تم تنظيف البيانات التجريبية');

    } catch (error) {
      console.log('❌ خطأ في اختبار النماذج:', error.message);
    }

    // 6. اختبار الذكاء الاصطناعي المتقدم (إذا كان متاحاً)
    if (testResults.ai) {
      console.log('\n6️⃣ اختبار الذكاء الاصطناعي المتقدم...');
      try {
        const sampleText = 'JavaScript هي لغة برمجة قوية ومرنة تُستخدم في تطوير المواقع والتطبيقات.';
        const keywordsResult = await aiService.extractKeywordsAndSummary(sampleText, {
          language: 'ar',
          maxKeywords: 3
        });

        if (keywordsResult.success) {
          console.log('✅ اختبار استخراج الكلمات المفتاحية نجح');
        } else {
          console.log('⚠️ اختبار استخراج الكلمات المفتاحية فشل');
        }
      } catch (error) {
        console.log('⚠️ خطأ في الاختبار المتقدم للذكاء الاصطناعي:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ خطأ عام في الاختبار الشامل:', error.message);
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    await databaseConfig.disconnect();
  }

  // عرض النتائج النهائية
  console.log('\n📊 نتائج الاختبار الشامل:');
  console.log('================================');
  console.log(`🗄️  قاعدة البيانات: ${testResults.database ? '✅ نجح' : '❌ فشل'}`);
  console.log(`🤖 الذكاء الاصطناعي: ${testResults.ai ? '✅ نجح' : '❌ فشل'}`);
  console.log(`📁 خدمة الملفات: ${testResults.fileService ? '✅ نجح' : '❌ فشل'}`);
  console.log(`📋 النماذج: ${testResults.models ? '✅ نجح' : '❌ فشل'}`);
  console.log(`🔗 التكامل: ${testResults.integration ? '✅ نجح' : '❌ فشل'}`);

  const successCount = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 النتيجة الإجمالية: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
  
  if (successCount === totalTests) {
    console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
  } else {
    console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
  }

  return testResults;
}

// تشغيل الاختبار الشامل
runCompleteTest().catch(console.error);
