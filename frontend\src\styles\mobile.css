/**
 * تصميم الجوال المحسن - Mobile Responsive Design
 */

/* ========== متغيرات الجوال ========== */
:root {
  --mobile-header-height: 60px;
  --mobile-sidebar-width: 280px;
  --mobile-padding: 16px;
  --mobile-border-radius: 12px;
  --mobile-font-size-small: 14px;
  --mobile-font-size-medium: 16px;
  --mobile-font-size-large: 18px;
  --mobile-touch-target: 44px;
}

/* ========== استعلامات الوسائط للجوال ========== */

/* الهواتف الصغيرة */
@media (max-width: 480px) {
  /* تخطيط عام */
  .main-layout {
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }

  /* الهيدر */
  .header {
    height: var(--mobile-header-height) !important;
    padding: 0 var(--mobile-padding) !important;
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    justify-content: space-between !important;
    align-items: center;
  }

  .header-logo {
    font-size: var(--mobile-font-size-medium) !important;
  }

  .header-logo img {
    width: 32px !important;
    height: 32px !important;
  }

  .header-controls {
    gap: 8px !important;
  }

  .header-controls button {
    width: var(--mobile-touch-target) !important;
    height: var(--mobile-touch-target) !important;
    min-width: unset !important;
    padding: 0 !important;
    border-radius: 50% !important;
  }

  /* الشريط الجانبي */
  .sidebar {
    position: fixed !important;
    top: var(--mobile-header-height);
    left: 0;
    width: var(--mobile-sidebar-width) !important;
    height: calc(100vh - var(--mobile-header-height)) !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
    z-index: 999;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .sidebar.expanded {
    transform: translateX(0) !important;
  }

  .sidebar-overlay {
    position: fixed;
    top: var(--mobile-header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .sidebar.expanded + .sidebar-overlay {
    opacity: 1;
    visibility: visible;
  }

  .sidebar-content {
    padding: var(--mobile-padding) !important;
  }

  .sidebar-nav {
    gap: 8px !important;
  }

  .sidebar-nav-item {
    padding: 12px var(--mobile-padding) !important;
    border-radius: var(--mobile-border-radius) !important;
    font-size: var(--mobile-font-size-medium) !important;
    min-height: var(--mobile-touch-target);
    display: flex;
    align-items: center;
  }

  .sidebar-nav-item svg {
    width: 20px !important;
    height: 20px !important;
  }

  /* منطقة المحتوى */
  .content-area {
    margin-top: var(--mobile-header-height) !important;
    margin-left: 0 !important;
    padding: var(--mobile-padding) !important;
    height: calc(100vh - var(--mobile-header-height)) !important;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* الأقسام */
  .section-container {
    padding: 0 !important;
    margin: 0 !important;
    height: 100% !important;
  }

  .section-header {
    padding: var(--mobile-padding) 0 !important;
    margin-bottom: var(--mobile-padding) !important;
  }

  .section-title {
    font-size: var(--mobile-font-size-large) !important;
    margin-bottom: 8px !important;
  }

  /* الأزرار */
  .btn, .button {
    min-height: var(--mobile-touch-target) !important;
    padding: 12px 16px !important;
    font-size: var(--mobile-font-size-medium) !important;
    border-radius: var(--mobile-border-radius) !important;
    touch-action: manipulation;
  }

  .btn-primary {
    width: 100% !important;
    margin-bottom: 12px;
  }

  /* النماذج */
  .form-group {
    margin-bottom: 16px !important;
  }

  .form-input, .form-textarea {
    width: 100% !important;
    min-height: var(--mobile-touch-target) !important;
    padding: 12px 16px !important;
    font-size: var(--mobile-font-size-medium) !important;
    border-radius: var(--mobile-border-radius) !important;
    border: 2px solid var(--border-color);
    transition: border-color 0.3s ease;
  }

  .form-input:focus, .form-textarea:focus {
    border-color: var(--primary-color);
    outline: none;
  }

  /* البطاقات */
  .card {
    border-radius: var(--mobile-border-radius) !important;
    padding: var(--mobile-padding) !important;
    margin-bottom: var(--mobile-padding) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  /* المحادثة */
  .chat-container {
    height: calc(100vh - var(--mobile-header-height) - 32px) !important;
    display: flex;
    flex-direction: column;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--mobile-padding);
    -webkit-overflow-scrolling: touch;
  }

  .chat-message {
    margin-bottom: 12px !important;
    padding: 12px !important;
    border-radius: var(--mobile-border-radius) !important;
    max-width: 85% !important;
  }

  .chat-input-container {
    padding: var(--mobile-padding);
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
  }

  .chat-input {
    width: 100% !important;
    min-height: var(--mobile-touch-target) !important;
    padding: 12px 16px !important;
    border-radius: var(--mobile-border-radius) !important;
    border: 2px solid var(--border-color);
    font-size: var(--mobile-font-size-medium) !important;
    resize: none;
  }

  /* الجداول */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table {
    min-width: 600px;
  }

  .table th, .table td {
    padding: 8px 12px !important;
    font-size: var(--mobile-font-size-small) !important;
  }

  /* الشبكة */
  .grid {
    grid-template-columns: 1fr !important;
    gap: var(--mobile-padding) !important;
  }

  .grid-2 {
    grid-template-columns: 1fr !important;
  }

  .grid-3 {
    grid-template-columns: 1fr !important;
  }

  .grid-4 {
    grid-template-columns: 1fr !important;
  }

  /* النوافذ المنبثقة */
  .modal {
    margin: var(--mobile-padding) !important;
    width: calc(100% - 32px) !important;
    max-height: calc(100vh - 64px) !important;
    border-radius: var(--mobile-border-radius) !important;
  }

  .modal-content {
    padding: var(--mobile-padding) !important;
  }

  /* التنبيهات */
  .alert {
    margin: 0 0 var(--mobile-padding) 0 !important;
    padding: 12px var(--mobile-padding) !important;
    border-radius: var(--mobile-border-radius) !important;
    font-size: var(--mobile-font-size-medium) !important;
  }

  /* التحميل */
  .loading-spinner {
    width: 32px !important;
    height: 32px !important;
  }

  /* إخفاء عناصر غير ضرورية على الجوال */
  .desktop-only {
    display: none !important;
  }

  /* إظهار عناصر الجوال فقط */
  .mobile-only {
    display: block !important;
  }

  /* تحسين النصوص */
  body {
    font-size: var(--mobile-font-size-medium) !important;
    line-height: 1.5 !important;
  }

  h1 {
    font-size: 24px !important;
  }

  h2 {
    font-size: 20px !important;
  }

  h3 {
    font-size: var(--mobile-font-size-large) !important;
  }

  /* تحسين التمرير */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* إزالة التأثيرات الثقيلة على الجوال */
  .animation-heavy {
    animation: none !important;
    transition: none !important;
  }

  /* تحسين الأداء */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* الأجهزة اللوحية */
@media (min-width: 481px) and (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .header {
    height: 70px !important;
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .sidebar {
    position: fixed !important;
    top: 70px;
    left: 0;
    width: 320px !important;
    height: calc(100vh - 70px) !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
    z-index: 999;
  }

  .sidebar.expanded {
    transform: translateX(0) !important;
  }

  .content-area {
    margin-top: 70px !important;
    margin-left: 0 !important;
    padding: 24px !important;
  }

  .grid-2 {
    grid-template-columns: 1fr 1fr !important;
  }

  .grid-3 {
    grid-template-columns: 1fr 1fr !important;
  }

  .grid-4 {
    grid-template-columns: 1fr 1fr !important;
  }
}

/* الشاشات الصغيرة جداً */
@media (max-width: 360px) {
  :root {
    --mobile-padding: 12px;
    --mobile-font-size-small: 12px;
    --mobile-font-size-medium: 14px;
    --mobile-font-size-large: 16px;
  }

  .sidebar {
    width: calc(100vw - 40px) !important;
  }

  .chat-message {
    max-width: 90% !important;
  }
}
