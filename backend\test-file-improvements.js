/**
 * اختبار تحسينات قراءة الملفات
 * Test File Reading Improvements
 */

const fileService = require('./src/services/fileService');
const aiService = require('./src/services/aiService');
const fs = require('fs').promises;
const path = require('path');

async function testFileReadingImprovements() {
  console.log('🧪 بدء اختبار تحسينات قراءة الملفات...\n');

  try {
    // إنشاء ملف نصي تجريبي
    const testContent = `
# مقدمة في البرمجة

## ما هي البرمجة؟
البرمجة هي عملية كتابة تعليمات للحاسوب لحل مشكلة معينة أو تنفيذ مهمة محددة.

## أساسيات البرمجة:
1. المتغيرات (Variables)
2. الدوال (Functions)  
3. الحلقات (Loops)
4. الشروط (Conditions)

### أمثلة عملية:
- إنشاء موقع ويب
- تطوير تطبيق جوال
- تحليل البيانات

> "البرمجة ليست مجرد كتابة كود، بل حل مشاكل بطريقة إبداعية"

## الخلاصة
البرمجة مهارة مهمة في العصر الحديث وتفتح آفاق واسعة للإبداع والابتكار.
    `.trim();

    const testFilePath = path.join(__dirname, 'test-lecture.txt');
    await fs.writeFile(testFilePath, testContent, 'utf8');
    console.log('📝 تم إنشاء ملف تجريبي:', testFilePath);

    // اختبار قراءة الملف مع التحليل المتقدم
    console.log('\n🔍 اختبار قراءة الملف مع التحليل المتقدم...');
    const fileResult = await fileService.readTextFile(testFilePath);
    
    console.log('📊 نتائج تحليل الملف:');
    console.log('- عدد الكلمات:', fileResult.wordCount);
    console.log('- عدد الجمل:', fileResult.sentenceCount);
    console.log('- عدد الفقرات:', fileResult.paragraphCount);
    console.log('- اللغة المكتشفة:', fileResult.detectedLanguage);
    console.log('- مستوى التعقيد:', fileResult.complexity);
    console.log('- وقت القراءة المقدر:', fileResult.readingTime, 'دقيقة');
    
    console.log('\n📋 تحليل البنية:');
    console.log('- العناوين:', fileResult.structure.headings.length);
    console.log('- القوائم:', fileResult.structure.lists.length);
    console.log('- الاقتباسات:', fileResult.structure.quotes.length);

    if (fileResult.structure.headings.length > 0) {
      console.log('\n📑 العناوين المكتشفة:');
      fileResult.structure.headings.forEach((heading, index) => {
        console.log(`  ${index + 1}. ${heading.text} (مستوى ${heading.level})`);
      });
    }

    if (fileResult.structure.lists.length > 0) {
      console.log('\n📝 القوائم المكتشفة:');
      fileResult.structure.lists.forEach((list, index) => {
        console.log(`  ${index + 1}. ${list.text} (${list.type})`);
      });
    }

    // اختبار التحليل المتقدم بالذكاء الاصطناعي
    console.log('\n🤖 اختبار التحليل المتقدم بالذكاء الاصطناعي...');
    const aiResult = await aiService.analyzeLectureContent(fileResult.content, fileResult, {
      language: 'ar',
      maxKeywords: 10
    });

    if (aiResult.success) {
      console.log('✅ نجح التحليل بالذكاء الاصطناعي');
      console.log('📄 الاستجابة الخام:', typeof aiResult.data, aiResult.data ? aiResult.data.substring(0, 200) + '...' : 'لا توجد بيانات');

      if (aiResult.data) {
        try {
          // محاولة استخراج JSON من النص
          let jsonText = aiResult.data;

          // البحث عن JSON في النص
          const jsonMatch = jsonText.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonText = jsonMatch[0];
          }

          const analysis = JSON.parse(jsonText);

          console.log('\n🔑 الكلمات المفتاحية:');
          if (analysis.keywords && Array.isArray(analysis.keywords)) {
            analysis.keywords.slice(0, 5).forEach((keyword, index) => {
              console.log(`  ${index + 1}. ${keyword.word || keyword} (${keyword.importance || 'غير محدد'})`);
            });
          } else {
            console.log('  لم يتم العثور على كلمات مفتاحية');
          }

          console.log('\n📄 الملخص:');
          if (analysis.summary?.brief) {
            console.log('  ', analysis.summary.brief);
          } else if (analysis.summary && typeof analysis.summary === 'string') {
            console.log('  ', analysis.summary);
          } else {
            console.log('  لم يتم العثور على ملخص');
          }

          console.log('\n🎯 التصنيف:');
          if (analysis.classification) {
            console.log('  - الموضوع الرئيسي:', analysis.classification.mainTopic || 'غير محدد');
            console.log('  - المجال الأكاديمي:', analysis.classification.academicField || 'غير محدد');
            console.log('  - مستوى الصعوبة:', analysis.classification.difficulty || 'غير محدد');
          } else {
            console.log('  لم يتم العثور على تصنيف');
          }

          console.log('\n💡 التوصيات التعليمية:');
          if (analysis.recommendations?.teachingMethods && Array.isArray(analysis.recommendations.teachingMethods)) {
            analysis.recommendations.teachingMethods.forEach((method, index) => {
              console.log(`  ${index + 1}. ${method}`);
            });
          } else {
            console.log('  لم يتم العثور على توصيات');
          }

        } catch (parseError) {
          console.log('❌ خطأ في تحليل JSON:', parseError.message);
          console.log('📄 محاولة عرض النص كما هو...');
          console.log(aiResult.data.substring(0, 800));
        }
      } else {
        console.log('❌ لا توجد بيانات في الاستجابة');
      }
    } else {
      console.log('❌ فشل التحليل بالذكاء الاصطناعي:', aiResult.error);
    }

    // اختبار شرح المحتوى
    console.log('\n📚 اختبار شرح المحتوى...');
    try {
      let analysisData = {};
      if (aiResult.success && aiResult.data) {
        try {
          const jsonMatch = aiResult.data.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            analysisData = JSON.parse(jsonMatch[0]);
          }
        } catch (e) {
          console.log('⚠️ لا يمكن تحليل بيانات التحليل، سيتم استخدام بيانات فارغة');
        }
      }

      const explanationResult = await aiService.explainLectureContent(
        fileResult.content,
        analysisData,
        'ما هي أهم المفاهيم في البرمجة؟',
        { language: 'ar', difficulty: 'beginner' }
      );

      if (explanationResult.success && explanationResult.data) {
        console.log('✅ نجح شرح المحتوى');
        console.log('📖 الشرح:', explanationResult.data.substring(0, 400) + '...');
      } else {
        console.log('❌ فشل شرح المحتوى:', explanationResult.error || 'لا توجد بيانات');
      }
    } catch (explanationError) {
      console.log('❌ خطأ في شرح المحتوى:', explanationError.message);
    }

    // تنظيف الملف التجريبي
    await fs.unlink(testFilePath);
    console.log('\n🧹 تم حذف الملف التجريبي');

    console.log('\n✅ انتهى اختبار تحسينات قراءة الملفات بنجاح!');

  } catch (error) {
    console.error('💥 خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testFileReadingImprovements();
}

module.exports = { testFileReadingImprovements };
