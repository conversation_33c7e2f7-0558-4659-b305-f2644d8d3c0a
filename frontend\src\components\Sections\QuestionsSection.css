/* Questions Section Styles */

.questions-container {
  height: calc(100vh - 70px);
  width: 100%;
  position: relative;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: calc(100vh - 70px);
}

.questions-container.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* منطقة المحتوى الرئيسي مع scroll محسن */
.questions-content {
  height: calc(100vh - 70px);
  max-height: calc(100vh - 70px);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.6) rgba(0, 0, 0, 0.05);
}

/* Scrollbar مخصص جميل */
.questions-content::-webkit-scrollbar {
  width: 8px;
}

.questions-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 8px 0;
}

.questions-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(37, 99, 235, 0.8) 100%
  );
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.questions-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 1) 0%,
    rgba(37, 99, 235, 1) 100%
  );
  transform: scaleX(1.2);
}

.questions-container.dark .questions-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.questions-container.dark .questions-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(96, 165, 250, 0.8) 0%,
    rgba(59, 130, 246, 0.8) 100%
  );
}

/* منطقة رفع الملفات */
.questions-upload-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.questions-upload-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.06);
}

.questions-container.dark .questions-upload-section {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.questions-upload-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.questions-container.dark .questions-upload-title {
  color: #f9fafb;
}

/* منطقة السحب والإفلات */
.questions-dropzone {
  border: 3px dashed #cbd5e1;
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.questions-dropzone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.questions-dropzone:hover::before {
  transform: translateX(100%);
}

.questions-dropzone:hover,
.questions-dropzone.dragover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.questions-container.dark .questions-dropzone {
  border-color: #475569;
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.questions-container.dark .questions-dropzone:hover,
.questions-container.dark .questions-dropzone.dragover {
  border-color: #60a5fa;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

.questions-dropzone-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.questions-dropzone-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.questions-container.dark .questions-dropzone-text {
  color: #d1d5db;
}

.questions-dropzone-subtext {
  font-size: 14px;
  color: #6b7280;
}

.questions-container.dark .questions-dropzone-subtext {
  color: #9ca3af;
}

.questions-file-input {
  display: none;
}

/* الملف المرفوع */
.questions-uploaded-file {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  margin-bottom: 20px;
}

.questions-container.dark .questions-uploaded-file {
  background: #064e3b;
  border-color: #065f46;
}

.questions-file-icon {
  width: 32px;
  height: 32px;
  background: #10b981;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.questions-file-info {
  flex: 1;
}

.questions-file-name {
  font-size: 14px;
  font-weight: 500;
  color: #065f46;
  margin-bottom: 2px;
}

.questions-container.dark .questions-file-name {
  color: #34d399;
}

.questions-file-size {
  font-size: 12px;
  color: #059669;
}

.questions-container.dark .questions-file-size {
  color: #10b981;
}

.questions-file-remove {
  padding: 4px;
  background: transparent;
  border: none;
  color: #ef4444;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.questions-file-remove:hover {
  background: #fee2e2;
}

.questions-container.dark .questions-file-remove:hover {
  background: #7f1d1d;
}

/* قسم التفضيلات */
.questions-preferences {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.questions-preferences:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.06);
}

.questions-container.dark .questions-preferences {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.questions-preferences-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.questions-container.dark .questions-preferences-title {
  color: #f9fafb;
}

.questions-preferences-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .questions-preferences-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.questions-preference-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.questions-preference-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.questions-container.dark .questions-preference-label {
  color: #d1d5db;
}

.questions-preference-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  color: #111827;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.questions-preference-select:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.questions-preference-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.questions-container.dark .questions-preference-select {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.questions-container.dark .questions-preference-select:hover {
  border-color: #60a5fa;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
}

.questions-container.dark .questions-preference-select:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.1);
}

/* معلومة نوع الأسئلة */
.question-type-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.questions-container.dark .question-type-info {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  border-color: #3b82f6;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.questions-container.dark .info-icon {
  background: #60a5fa;
}

.info-text {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
  flex: 1;
}

.questions-container.dark .info-text {
  color: #bfdbfe;
}

.questions-preference-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #111827;
  font-size: 14px;
  transition: all 0.3s ease;
}

.questions-preference-input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.questions-container.dark .questions-preference-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.questions-container.dark .questions-preference-input:focus {
  border-color: #34d399;
  box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.1);
}

/* زر توليد الأسئلة */
.questions-generate-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.questions-generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.questions-generate-btn:hover::before {
  transform: translateX(100%);
}

.questions-generate-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.questions-generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.questions-generate-btn.loading {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 4px 14px rgba(107, 114, 128, 0.3);
}

.questions-generate-btn.disabled {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  cursor: not-allowed;
  opacity: 0.7;
}

/* حالة الاستخدام اليومي */
.usage-status {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.questions-container.dark .usage-status {
  background: rgba(96, 165, 250, 0.1);
  border-color: rgba(96, 165, 250, 0.2);
}

.usage-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.usage-text {
  font-size: 14px;
  font-weight: 500;
  color: #3b82f6;
  text-align: center;
}

.questions-container.dark .usage-text {
  color: #60a5fa;
}

.usage-bar {
  height: 6px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #3b82f6 50%, #ef4444 100%);
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* شريط البحث والفلاتر */
.questions-header {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  align-items: center;
}

.questions-search-wrapper {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.questions-search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 14px;
  outline: none;
  background-color: white;
  color: #111827;
  transition: all 0.3s ease;
}

.questions-search-input:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.questions-container.dark .questions-search-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.questions-container.dark .questions-search-input:focus {
  border-color: #34d399;
  box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.1);
}

.questions-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.questions-container.dark .questions-search-icon {
  color: #6b7280;
}

/* أزرار الفلاتر */
.questions-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.questions-filter-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.questions-filter-btn:hover {
  border-color: #10b981;
  background: #f0fdf4;
  color: #059669;
}

.questions-filter-btn.active {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.questions-container.dark .questions-filter-btn {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.questions-container.dark .questions-filter-btn:hover {
  border-color: #34d399;
  background: #064e3b;
  color: #34d399;
}

.questions-container.dark .questions-filter-btn.active {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

/* شبكة الأسئلة */
.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* بطاقة السؤال */
.question-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  color: #374151;
}

.question-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.questions-container.dark .question-card {
  background: #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #e5e7eb;
}

.questions-container.dark .question-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* رأس البطاقة */
.question-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.question-difficulty {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.question-difficulty.easy {
  background: #dcfce7;
  color: #166534;
}

.questions-container.dark .question-difficulty.easy {
  background: #065f46;
  color: #a7f3d0;
}

.question-difficulty.medium {
  background: #fef3c7;
  color: #92400e;
}

.questions-container.dark .question-difficulty.medium {
  background: #92400e;
  color: #fde68a;
}

.question-difficulty.hard {
  background: #fee2e2;
  color: #991b1b;
}

.questions-container.dark .question-difficulty.hard {
  background: #991b1b;
  color: #fecaca;
}

.questions-container.dark .question-difficulty.easy {
  background: #064e3b;
  color: #34d399;
}

.questions-container.dark .question-difficulty.medium {
  background: #451a03;
  color: #fbbf24;
}

.questions-container.dark .question-difficulty.hard {
  background: #7f1d1d;
  color: #f87171;
}

/* محتوى السؤال */
.question-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1.4;
}

.questions-container.dark .question-title {
  color: #f9fafb;
}

.question-preview {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 16px;
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.question-preview span {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  color: #374151;
}

.questions-container.dark .question-preview {
  color: #9ca3af;
  background: #374151;
  border-left-color: #60a5fa;
}

.questions-container.dark .question-preview span {
  color: #d1d5db;
}

/* تذييل البطاقة */
.question-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.questions-container.dark .question-card-footer {
  border-top-color: #374151;
}

.question-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.questions-container.dark .question-stats {
  color: #9ca3af;
}

.question-action-btn {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.question-action-btn:hover {
  background: #059669;
  transform: scale(1.05);
}

/* زر إضافة سؤال جديد */
.questions-fab {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.questions-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(16, 185, 129, 0.5);
}

.questions-container.rtl .questions-fab {
  right: auto;
  left: 30px;
}

/* حالة فارغة */
.questions-empty {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.questions-container.dark .questions-empty {
  color: #9ca3af;
}

.questions-empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.questions-empty-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #111827;
}

.questions-container.dark .questions-empty-title {
  color: #f9fafb;
}

.questions-empty-subtitle {
  font-size: 14px;
  margin-bottom: 24px;
}

.questions-empty-btn {
  padding: 12px 24px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.questions-empty-btn:hover {
  background: #059669;
  transform: translateY(-2px);
}

/* نموذج الأسئلة التفاعلي */
.quiz-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.quiz-content {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.questions-container.dark .quiz-content {
  background: #1f2937;
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.quiz-progress {
  flex: 1;
  margin-right: 1rem;
}

.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  transition: width 0.3s ease;
}

.question-counter {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.quiz-close {
  width: 40px;
  height: 40px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quiz-close:hover {
  background: #ef4444;
  color: white;
}

.question-card-quiz {
  background: #f8fafc;
  border-radius: 16px;
  padding: 2rem;
}

.questions-container.dark .question-card-quiz {
  background: #374151;
}

.question-text {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.questions-container.dark .question-text {
  color: #f9fafb;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.questions-container.dark .option-btn {
  background: #374151;
  border-color: #4b5563;
}

.option-btn:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.questions-container.dark .option-btn:hover {
  border-color: #60a5fa;
  background: #1e3a8a;
}

.option-btn.selected {
  border-color: #3b82f6;
  background: #dbeafe;
}

.questions-container.dark .option-btn.selected {
  border-color: #60a5fa;
  background: #1e3a8a;
}

.option-btn.correct {
  border-color: #10b981;
  background: #d1fae5;
}

.questions-container.dark .option-btn.correct {
  border-color: #34d399;
  background: #064e3b;
}

.option-btn.incorrect {
  border-color: #ef4444;
  background: #fee2e2;
}

.questions-container.dark .option-btn.incorrect {
  border-color: #f87171;
  background: #7f1d1d;
}

.option-letter {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.option-btn.correct .option-letter {
  background: #10b981;
}

.option-btn.incorrect .option-letter {
  background: #ef4444;
}

.option-text {
  flex: 1;
  font-size: 16px;
  color: #374151;
}

.questions-container.dark .option-text {
  color: #d1d5db;
}

.explanation-box {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.questions-container.dark .explanation-box {
  background: #1e3a8a;
  border-color: #1d4ed8;
}

.explanation-box h4 {
  color: #1d4ed8;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.questions-container.dark .explanation-box h4 {
  color: #60a5fa;
}

.quiz-controls {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
}

.control-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn.primary {
  background: #3b82f6;
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: #2563eb;
}

.control-btn.secondary {
  background: #f3f4f6;
  color: #374151;
}

.control-btn.secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* نموذج النتائج */
.results-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.results-content {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.questions-container.dark .results-content {
  background: #1f2937;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  color: white;
}

.score-number {
  font-size: 2rem;
  font-weight: 700;
}

.score-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

.results-summary {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.result-item {
  text-align: center;
}

.result-item.correct {
  color: #10b981;
}

.result-item.incorrect {
  color: #ef4444;
}

.result-item span:first-child {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.results-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.restart-btn, .close-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.restart-btn {
  background: #3b82f6;
  color: white;
}

.restart-btn:hover {
  background: #2563eb;
}

.close-btn {
  background: #f3f4f6;
  color: #374151;
}

.close-btn:hover {
  background: #e5e7eb;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .questions-content {
    padding: 1rem;
  }

  .questions-upload-section,
  .questions-preferences {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .questions-preferences-grid {
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .question-type-info,
  .usage-status {
    padding: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .questions-generate-btn {
    padding: 0.625rem 1.25rem;
    font-size: 14px;
    margin-top: 0.75rem;
  }
}

@media (max-width: 768px) {
  .questions-container {
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
  }

  .questions-content {
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    padding: 0.75rem;
  }
}

/* إعدادات الأسئلة الأنيقة الجديدة */
.questions-preferences-elegant {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  max-height: 280px;
}

.questions-preferences-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 20px 20px 0 0;
}

.questions-preferences-elegant:hover {
  transform: translateY(-3px);
  box-shadow:
    0 15px 50px rgba(0, 0, 0, 0.12),
    0 8px 25px rgba(0, 0, 0, 0.06);
}

.questions-container.dark .questions-preferences-elegant {
  background: rgba(31, 41, 55, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.preferences-header {
  margin-bottom: 18px;
}

.preferences-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 10px;
}

.questions-container.dark .preferences-title {
  color: #f9fafb;
}

.preferences-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #3b82f6;
  font-size: 0.85rem;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.questions-container.dark .preferences-note {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), rgba(168, 85, 247, 0.15));
  border-color: rgba(96, 165, 250, 0.3);
  color: #60a5fa;
}

.preferences-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 18px;
}

.preference-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preference-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.questions-container.dark .preference-label {
  color: #d1d5db;
}

.label-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.preference-select {
  padding: 14px 18px;
  border: 2px solid #e5e7eb;
  border-radius: 14px;
  background: #ffffff;
  color: #111827;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.preference-select:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.preference-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.questions-container.dark .preference-select {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.questions-container.dark .preference-select:hover {
  border-color: #60a5fa;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);
}

.questions-container.dark .preference-select:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.1);
}

.preference-select option {
  padding: 12px;
  background: #ffffff;
  color: #111827;
  font-weight: 500;
}

.questions-container.dark .preference-select option {
  background: #374151;
  color: #f9fafb;
}

/* زر التوليد الأنيق */
.generate-btn-elegant {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 18px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.generate-btn-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.generate-btn-elegant:hover::before {
  left: 100%;
}

.generate-btn-elegant:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.generate-btn-elegant:active {
  transform: translateY(0);
}

.generate-btn-elegant.loading {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
  cursor: not-allowed;
  transform: none;
}

.generate-btn-elegant.disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  transition: transform 0.3s ease;
}

.generate-btn-elegant:hover .btn-icon {
  transform: scale(1.1);
}

.generate-btn-elegant.loading .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* رسالة عدم وجود خيارات */
.no-options-message {
  text-align: center;
  padding: 2rem;
  background: rgba(249, 250, 251, 0.8);
  border: 2px dashed #d1d5db;
  border-radius: 16px;
  margin: 1rem 0;
}

.questions-container.dark .no-options-message {
  background: rgba(31, 41, 55, 0.8);
  border-color: #4b5563;
}

.no-options-message p {
  color: #6b7280;
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.questions-container.dark .no-options-message p {
  color: #9ca3af;
}

.mark-as-read-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mark-as-read-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.mark-as-read-btn:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .questions-preferences-elegant {
    padding: 18px;
    margin-bottom: 18px;
  }

  .preferences-content {
    grid-template-columns: 1fr;
    gap: 14px;
  }

  .generate-btn-elegant {
    padding: 12px 18px;
    font-size: 0.9rem;
  }

  .no-options-message {
    padding: 1.5rem;
  }
}

/* رسالة حالة الزر */
.button-status-message {
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(249, 250, 251, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.questions-container.dark .button-status-message {
  background: rgba(31, 41, 55, 0.9);
  border-color: #374151;
}
