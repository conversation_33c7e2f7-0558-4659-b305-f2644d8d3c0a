const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const fileService = require('../services/fileService');
const auth = require('../middleware/auth');

const router = express.Router();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/temp/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function (req, file, cb) {
    // قبول أنواع الملفات المدعومة
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    
    if (allowedTypes.includes(file.mimetype) || 
        file.originalname.toLowerCase().endsWith('.txt') ||
        file.originalname.toLowerCase().endsWith('.pdf') ||
        file.originalname.toLowerCase().endsWith('.doc') ||
        file.originalname.toLowerCase().endsWith('.docx')) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF, Word, أو نص عادي.'));
    }
  }
});

/**
 * استخراج النص من الملفات المرفوعة
 * POST /api/files/extract-text
 */
router.post('/extract-text', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم رفع أي ملف'
      });
    }

    console.log('📄 معالجة ملف:', req.file.originalname, 'نوع:', req.file.mimetype);

    let fileContent;
    const filePath = req.file.path;
    const fileExtension = path.extname(req.file.originalname).toLowerCase();

    try {
      // قراءة الملف حسب نوعه
      if (fileExtension === '.pdf') {
        console.log('📖 قراءة ملف PDF...');
        try {
          fileContent = await fileService.readPDF(filePath);
        } catch (pdfError) {
          console.error('❌ خطأ في قراءة PDF:', pdfError.message);
          throw new Error('فشل في قراءة ملف PDF. تأكد من أن الملف غير محمي بكلمة مرور وليس تالفاً.');
        }
      } else if (fileExtension === '.doc' || fileExtension === '.docx') {
        console.log('📝 قراءة ملف Word...');
        try {
          fileContent = await fileService.readWord(filePath);
        } catch (wordError) {
          console.error('❌ خطأ في قراءة Word:', wordError.message);
          throw new Error('فشل في قراءة ملف Word. تأكد من أن الملف ليس تالفاً.');
        }
      } else {
        console.log('📄 قراءة ملف نصي...');
        try {
          fileContent = await fileService.readPlainText(filePath);
        } catch (textError) {
          console.error('❌ خطأ في قراءة النص:', textError.message);
          throw new Error('فشل في قراءة الملف النصي. تأكد من أن الملف يحتوي على نص صالح.');
        }
      }

      // التحقق من وجود محتوى
      if (!fileContent || !fileContent.content || fileContent.content.trim().length === 0) {
        throw new Error('الملف فارغ أو لا يحتوي على نص قابل للقراءة.');
      }

      console.log('✅ تم قراءة الملف بنجاح:', {
        contentLength: fileContent.content.length,
        wordCount: fileContent.wordCount,
        language: fileContent.detectedLanguage
      });

      // تنظيف الملف المؤقت
      try {
        await fs.unlink(filePath);
        console.log('🧹 تم حذف الملف المؤقت');
      } catch (cleanupError) {
        console.warn('⚠️ لم يتم حذف الملف المؤقت:', cleanupError.message);
      }

      // إرجاع النتيجة
      res.json({
        success: true,
        message: 'تم استخراج النص بنجاح',
        data: {
          content: fileContent.content,
          wordCount: fileContent.wordCount,
          charCount: fileContent.charCount,
          language: fileContent.detectedLanguage,
          complexity: fileContent.complexity,
          readingTime: fileContent.readingTime,
          structure: fileContent.structure
        }
      });

    } catch (processingError) {
      console.error('❌ خطأ في معالجة الملف:', processingError);
      
      // تنظيف الملف المؤقت في حالة الخطأ
      try {
        await fs.unlink(filePath);
      } catch (cleanupError) {
        console.warn('⚠️ لم يتم حذف الملف المؤقت بعد الخطأ:', cleanupError.message);
      }

      res.status(500).json({
        success: false,
        message: 'فشل في معالجة الملف',
        error: processingError.message
      });
    }

  } catch (error) {
    console.error('💥 خطأ عام في استخراج النص:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error.message
    });
  }
});

/**
 * معلومات حول أنواع الملفات المدعومة
 * GET /api/files/supported-types
 */
router.get('/supported-types', (req, res) => {
  res.json({
    success: true,
    data: {
      supportedTypes: [
        {
          type: 'PDF',
          extensions: ['.pdf'],
          mimeTypes: ['application/pdf'],
          description: 'ملفات PDF'
        },
        {
          type: 'Word',
          extensions: ['.doc', '.docx'],
          mimeTypes: [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ],
          description: 'ملفات Microsoft Word'
        },
        {
          type: 'Text',
          extensions: ['.txt'],
          mimeTypes: ['text/plain'],
          description: 'ملفات نصية عادية'
        }
      ],
      maxFileSize: '10MB',
      features: [
        'استخراج النص',
        'تحليل البنية',
        'كشف اللغة',
        'حساب الإحصائيات',
        'تقدير وقت القراءة'
      ]
    }
  });
});

module.exports = router;
