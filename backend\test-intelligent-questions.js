/**
 * اختبار نظام توليد الأسئلة الذكية
 */

const aiService = require('./src/services/aiService');
const fs = require('fs').promises;
const path = require('path');

async function testIntelligentQuestions() {
  console.log('🧪 اختبار نظام توليد الأسئلة الذكية...\n');

  try {
    // محتوى تجريبي في البرمجة
    const programmingContent = `
# مقدمة في البرمجة الكائنية (Object-Oriented Programming)

## ما هي البرمجة الكائنية؟
البرمجة الكائنية هي نموذج برمجي يعتمد على مفهوم "الكائنات" (Objects) التي تحتوي على بيانات (attributes) ووظائف (methods).

## المفاهيم الأساسية:

### 1. الفئات (Classes)
الفئة هي قالب أو مخطط لإنشاء الكائنات. تحدد الفئة الخصائص والسلوكيات التي ستمتلكها الكائنات.

\`\`\`javascript
class Car {
  constructor(brand, model, year) {
    this.brand = brand;
    this.model = model;
    this.year = year;
  }
  
  start() {
    console.log("السيارة تعمل الآن");
  }
  
  stop() {
    console.log("السيارة متوقفة");
  }
}
\`\`\`

### 2. الكائنات (Objects)
الكائن هو مثيل (instance) من فئة معينة. يحتوي على قيم محددة للخصائص المعرفة في الفئة.

\`\`\`javascript
const myCar = new Car("Toyota", "Camry", 2023);
myCar.start(); // السيارة تعمل الآن
\`\`\`

### 3. التغليف (Encapsulation)
التغليف هو إخفاء التفاصيل الداخلية للكائن وإظهار واجهة بسيطة للتفاعل معه.

### 4. الوراثة (Inheritance)
الوراثة تسمح لفئة جديدة بوراثة خصائص وسلوكيات من فئة موجودة.

\`\`\`javascript
class ElectricCar extends Car {
  constructor(brand, model, year, batteryCapacity) {
    super(brand, model, year);
    this.batteryCapacity = batteryCapacity;
  }
  
  charge() {
    console.log("جاري شحن البطارية");
  }
}
\`\`\`

### 5. تعدد الأشكال (Polymorphism)
تعدد الأشكال يسمح لكائنات مختلفة بالاستجابة لنفس الرسالة بطرق مختلفة.

## فوائد البرمجة الكائنية:
- إعادة استخدام الكود
- سهولة الصيانة
- تنظيم أفضل للكود
- قابلية التوسع

## التطبيقات العملية:
تستخدم البرمجة الكائنية في تطوير:
- تطبيقات سطح المكتب
- تطبيقات الويب
- الألعاب
- أنظمة إدارة قواعد البيانات
    `.trim();

    console.log('📖 المحتوى التجريبي:', programmingContent.substring(0, 200) + '...');
    console.log('📊 طول المحتوى:', programmingContent.length, 'حرف');

    // اختبار تحليل المجال
    console.log('\n🔍 اختبار تحليل مجال المحتوى...');
    const domainAnalysis = aiService.analyzeContentDomain(programmingContent);
    console.log('📊 نتيجة تحليل المجال:', domainAnalysis);

    // اختبار توليد الأسئلة الذكية
    console.log('\n🧠 اختبار توليد الأسئلة الذكية...');
    const questionsResult = await aiService.generateIntelligentQuestions(
      programmingContent, 
      {
        classification: {
          mainTopic: 'البرمجة الكائنية',
          academicField: 'علوم الحاسوب',
          subTopics: ['الفئات', 'الكائنات', 'الوراثة', 'التغليف'],
          difficulty: 'intermediate'
        },
        keywords: [
          { word: 'البرمجة الكائنية', importance: 'high' },
          { word: 'الفئات', importance: 'high' },
          { word: 'الكائنات', importance: 'high' },
          { word: 'الوراثة', importance: 'medium' },
          { word: 'التغليف', importance: 'medium' }
        ]
      },
      {
        questionCount: 5,
        difficulty: 'medium',
        language: 'ar'
      }
    );

    if (questionsResult.success) {
      console.log('✅ نجح توليد الأسئلة الذكية');
      
      try {
        const questions = JSON.parse(questionsResult.data || questionsResult.content);
        
        console.log('\n📋 الأسئلة المولدة:');
        if (questions.questions && Array.isArray(questions.questions)) {
          questions.questions.forEach((q, index) => {
            console.log(`\n${index + 1}. ${q.questionText}`);
            console.log(`   المستوى المعرفي: ${q.cognitiveLevel || 'غير محدد'}`);
            console.log(`   الفئة: ${q.category || 'غير محدد'}`);
            console.log(`   الخيارات: ${q.options?.length || 0} خيارات`);
            console.log(`   الإجابة الصحيحة: ${q.correctAnswer}`);
            if (q.relatedConcepts && q.relatedConcepts.length > 0) {
              console.log(`   المفاهيم المرتبطة: ${q.relatedConcepts.join(', ')}`);
            }
          });
        }

        if (questions.metadata) {
          console.log('\n📊 معلومات إضافية:');
          console.log('   - مجال المحتوى:', questions.metadata.contentDomain);
          console.log('   - توزيع المستويات المعرفية:', JSON.stringify(questions.metadata.cognitiveDistribution, null, 2));
        }

      } catch (parseError) {
        console.log('❌ خطأ في تحليل JSON:', parseError.message);
        console.log('📄 الاستجابة الخام:', questionsResult.data?.substring(0, 500) + '...');
      }
    } else {
      console.log('❌ فشل توليد الأسئلة:', questionsResult.error);
    }

    console.log('\n✅ انتهى اختبار نظام الأسئلة الذكية!');

  } catch (error) {
    console.error('💥 خطأ في الاختبار:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testIntelligentQuestions();
}

module.exports = { testIntelligentQuestions };
