# 📚 توثيق APIs - المنصة التعليمية الذكية

## 🎯 نظرة عامة

هذا التوثيق يشرح جميع APIs المتاحة في المنصة التعليمية الذكية، مع أمثلة على الاستخدام والاستجابات المتوقعة.

## 🔐 المصادقة

جميع APIs المحمية تتطلب رمز مصادقة JWT في header:

```
Authorization: Bearer <token>
```

## 📋 فهرس APIs

### 1. المصادقة والتفويض

#### تسجيل مستخدم جديد
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "أحمد محمد",
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم إنشاء الحساب بنجاح",
  "data": {
    "user": {
      "id": "user_id",
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "role": "student",
      "level": "مبتدئ"
    },
    "token": "jwt_token",
    "refreshToken": "refresh_token"
  }
}
```

#### تسجيل الدخول
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}
```

### 2. المحاضرات

#### رفع محاضرة جديدة
```http
POST /api/lectures
Authorization: Bearer <token>
Content-Type: multipart/form-data

title: "مقدمة في JavaScript"
file: [PDF/DOC/TXT file]
```

#### الحصول على قائمة المحاضرات
```http
GET /api/lectures?page=1&limit=10&search=javascript
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "lectures": [
      {
        "id": "lecture_id",
        "title": "مقدمة في JavaScript",
        "summary": "ملخص المحاضرة...",
        "keywords": ["JavaScript", "برمجة"],
        "isProcessed": true,
        "createdAt": "2025-06-11T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

### 3. الأسئلة

#### توليد أسئلة من محاضرة
```http
POST /api/questions/generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "lectureId": "lecture_id",
  "questionCount": 5,
  "difficulty": "medium",
  "questionTypes": ["multiple_choice", "true_false"]
}
```

#### الإجابة على سؤال
```http
POST /api/questions/:id/answer
Authorization: Bearer <token>
Content-Type: application/json

{
  "userAnswer": "الإجابة المختارة",
  "timeSpent": 30
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "إجابة صحيحة!",
  "data": {
    "isCorrect": true,
    "correctAnswer": "الإجابة الصحيحة",
    "explanation": "شرح الإجابة...",
    "pointsEarned": 10,
    "totalPoints": 150
  }
}
```

### 4. المهام

#### الحصول على قائمة المهام
```http
GET /api/tasks?category=web_development&difficulty=beginner&language=javascript
```

#### إرسال حل للمهمة
```http
POST /api/tasks/:id/submit
Authorization: Bearer <token>
Content-Type: application/json

{
  "code": "function factorial(n) { return n <= 1 ? 1 : n * factorial(n - 1); }"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم حل المهمة بنجاح!",
  "data": {
    "result": "passed",
    "score": 100,
    "passedTests": 5,
    "totalTests": 5,
    "pointsEarned": 25,
    "testResults": [
      {
        "passed": true,
        "error": null
      }
    ]
  }
}
```

### 5. التقارير

#### إنشاء تقرير جديد
```http
POST /api/reports
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "تقرير الأداء الشهري",
  "reportType": "performance",
  "dateRange": {
    "startDate": "2025-05-01T00:00:00Z",
    "endDate": "2025-05-31T23:59:59Z"
  },
  "includedData": {
    "lectures": true,
    "questions": true,
    "tasks": true,
    "analytics": true
  }
}
```

### 6. التحليلات

#### الحصول على التحليلات اليومية
```http
GET /api/analytics/daily?date=2025-06-11
Authorization: Bearer <token>
```

#### تحديث النشاط
```http
POST /api/analytics/activity
Authorization: Bearer <token>
Content-Type: application/json

{
  "activityType": "questionsAnswered",
  "increment": 1,
  "metadata": {
    "category": "web_development",
    "difficulty": "medium"
  }
}
```

#### الحصول على تحليل الأداء الذكي
```http
GET /api/analytics/insights?period=30
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "insights": {
      "overallPerformance": {
        "score": 85,
        "grade": "جيد جداً",
        "summary": "أداء ممتاز في البرمجة..."
      },
      "strengths": ["حل المشاكل", "JavaScript"],
      "weaknesses": ["قواعد البيانات"],
      "recommendations": [
        "ركز على تعلم SQL",
        "مارس المزيد من التمارين"
      ]
    }
  }
}
```

## 📊 رموز الحالة

- `200` - نجح الطلب
- `201` - تم إنشاء المورد بنجاح
- `400` - خطأ في البيانات المرسلة
- `401` - غير مصرح (رمز مصادقة مطلوب)
- `403` - ممنوع (ليس لديك صلاحية)
- `404` - المورد غير موجود
- `500` - خطأ داخلي في الخادم

## 🔍 معاملات الاستعلام الشائعة

### الترقيم (Pagination)
```
?page=1&limit=10
```

### البحث
```
?search=javascript&q=برمجة
```

### التصفية
```
?category=web_development&difficulty=medium&language=javascript
```

### الترتيب
```
?sort=createdAt&order=desc
```

## 🛡️ الأمان

### معدل الطلبات
- الحد الأقصى: 100 طلب في الدقيقة لكل IP
- APIs المصادقة: 5 طلبات في الدقيقة

### التحقق من البيانات
- جميع البيانات المدخلة يتم التحقق منها
- تنظيف البيانات من MongoDB injection
- تشفير كلمات المرور باستخدام bcrypt

## 🧪 اختبار APIs

### باستخدام curl
```bash
# تسجيل مستخدم
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"123456","confirmPassword":"123456"}'

# تسجيل الدخول
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'
```

### باستخدام JavaScript
```javascript
// تسجيل مستخدم
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'Test User',
    email: '<EMAIL>',
    password: '123456',
    confirmPassword: '123456'
  })
});

const data = await response.json();
```

## 📝 ملاحظات مهمة

1. **الرموز المميزة**: تنتهي صلاحيتها خلال 7 أيام
2. **رفع الملفات**: الحد الأقصى 10MB
3. **الأنواع المدعومة**: PDF, DOC, DOCX, TXT
4. **اللغة**: جميع الاستجابات تدعم العربية والإنجليزية
5. **التوقيت**: جميع التواريخ بصيغة ISO 8601

---

**للمساعدة والدعم، يرجى التواصل مع فريق التطوير** 📞
