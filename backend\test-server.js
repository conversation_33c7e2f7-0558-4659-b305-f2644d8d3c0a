/**
 * اختبار بسيط للخادم
 */

const express = require('express');
const cors = require('cors');

const app = express();

// Middleware أساسي
app.use(cors());
app.use(express.json());

// مسار اختبار
app.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'الخادم يعمل بشكل صحيح',
    timestamp: new Date().toISOString()
  });
});

// مسار تسجيل مستخدم بسيط
app.post('/test/register', (req, res) => {
  const { name, email } = req.body;
  
  if (!name || !email) {
    return res.status(400).json({
      success: false,
      message: 'الاسم والبريد الإلكتروني مطلوبان'
    });
  }

  res.json({
    success: true,
    message: 'تم التسجيل بنجاح',
    data: { name, email }
  });
});

const PORT = 3001;

app.listen(PORT, () => {
  console.log(`🚀 خادم الاختبار يعمل على المنفذ ${PORT}`);
  console.log(`🌐 اختبار: http://localhost:${PORT}/test`);
});

module.exports = app;
