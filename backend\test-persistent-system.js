/**
 * اختبار النظام المستمر مع حفظ الحالة
 * Persistent System Test with State Management
 */

require('dotenv').config();
const databaseConfig = require('./src/config/database');
const { User, Chat, Analytics } = require('./src/models');
const { cleanupExpiredChats, getStorageStats } = require('./src/utils/cleanup');

async function testPersistentSystem() {
  console.log('🧪 بدء اختبار النظام المستمر مع حفظ الحالة...\n');

  try {
    // 1. الاتصال بقاعدة البيانات
    console.log('1️⃣ الاتصال بقاعدة البيانات...');
    await databaseConfig.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 2. إنشاء مستخدم تجريبي
    console.log('\n2️⃣ إنشاء مستخدم تجريبي...');
    const testUser = new User({
      name: 'مستخدم النظام المستمر',
      email: `persistent_test_${Date.now()}@example.com`,
      password: '123456',
      preferences: {
        theme: 'dark',
        language: 'ar'
      }
    });

    await testUser.save();
    console.log(`✅ تم إنشاء المستخدم: ${testUser.name}`);
    console.log(`📧 البريد الإلكتروني: ${testUser.email}`);
    console.log(`🎨 المظهر: ${testUser.preferences.theme}`);
    console.log(`🌍 اللغة: ${testUser.preferences.language}`);

    // 3. إنشاء جلسة محادثة
    console.log('\n3️⃣ إنشاء جلسة محادثة...');
    const chatSession = await Chat.createSession(testUser._id, 'lectures', {
      language: 'ar',
      deviceInfo: {
        browser: 'Chrome Test',
        os: 'Windows Test',
        device: 'Desktop Test'
      }
    });

    console.log(`✅ تم إنشاء جلسة المحادثة: ${chatSession.sessionId}`);
    console.log(`📍 القسم: ${chatSession.section}`);
    console.log(`🕐 وقت البداية: ${chatSession.sessionInfo.startTime.toLocaleString('ar-SA')}`);

    // 4. إضافة رسائل للمحادثة
    console.log('\n4️⃣ إضافة رسائل للمحادثة...');
    
    const userMessages = [
      'مرحباً، أريد تعلم JavaScript',
      'كيف يمكنني إنشاء دالة؟',
      'ما هي المتغيرات؟'
    ];

    const aiResponses = [
      'مرحباً بك! سأساعدك في تعلم JavaScript. إنها لغة برمجة رائعة!',
      'يمكنك إنشاء دالة باستخدام الكلمة المفتاحية function. مثال: function myFunction() { }',
      'المتغيرات هي حاويات لتخزين البيانات. يمكنك إنشاؤها باستخدام let أو const أو var.'
    ];

    for (let i = 0; i < userMessages.length; i++) {
      // رسالة المستخدم
      chatSession.addMessage({
        type: 'user',
        content: userMessages[i],
        metadata: { messageIndex: i + 1 }
      });

      // رد الذكاء الاصطناعي
      chatSession.addMessage({
        type: 'ai',
        content: aiResponses[i],
        metadata: {
          model: 'test-model',
          processingTime: Math.random() * 1000,
          tokens: Math.floor(Math.random() * 100) + 50,
          confidence: 0.9
        }
      });
    }

    await chatSession.save();
    console.log(`✅ تم إضافة ${chatSession.messages.length} رسالة للمحادثة`);
    console.log(`📊 إحصائيات المحادثة:`);
    console.log(`   - إجمالي الرسائل: ${chatSession.stats.totalMessages}`);
    console.log(`   - رسائل المستخدم: ${chatSession.stats.userMessages}`);
    console.log(`   - رسائل الذكاء الاصطناعي: ${chatSession.stats.aiMessages}`);
    console.log(`   - متوسط وقت الاستجابة: ${chatSession.stats.averageResponseTime.toFixed(2)} مللي ثانية`);

    // 5. إنشاء تحليلات يومية
    console.log('\n5️⃣ إنشاء تحليلات يومية...');
    const todayAnalytics = new Analytics({
      userId: testUser._id,
      date: new Date()
    });

    // تحديث الأنشطة
    todayAnalytics.updateActivity('lecturesViewed', 3);
    todayAnalytics.updateActivity('questionsAnswered', 5);
    todayAnalytics.updateActivity('tasksCompleted', 2);
    todayAnalytics.updateActivity('chatMessages', chatSession.stats.userMessages);

    // تحديث الأداء
    todayAnalytics.updatePerformance(5, 4, 85); // 5 أسئلة، 4 صحيحة، 85 نقطة

    // تحديث الأنماط
    todayAnalytics.updatePattern('categoryPerformance', 'web_development', 3);
    todayAnalytics.updatePattern('languageUsage', 'javascript', 5);
    todayAnalytics.updatePattern('preferredDifficulty', 'medium', 4);
    todayAnalytics.updatePattern('timeDistribution', 'evening', 1);

    await todayAnalytics.save();
    console.log(`✅ تم إنشاء التحليلات اليومية`);
    console.log(`📈 الأنشطة:`);
    console.log(`   - محاضرات مشاهدة: ${todayAnalytics.activities.lecturesViewed}`);
    console.log(`   - أسئلة مجابة: ${todayAnalytics.activities.questionsAnswered}`);
    console.log(`   - مهام مكتملة: ${todayAnalytics.activities.tasksCompleted}`);
    console.log(`   - رسائل محادثة: ${todayAnalytics.activities.chatMessages}`);

    // 6. اختبار استمرارية البيانات
    console.log('\n6️⃣ اختبار استمرارية البيانات...');
    
    // محاكاة إعادة تشغيل - البحث عن البيانات المحفوظة
    const savedUser = await User.findById(testUser._id);
    const savedChat = await Chat.findById(chatSession._id);
    const savedAnalytics = await Analytics.findById(todayAnalytics._id);

    console.log(`✅ المستخدم محفوظ: ${savedUser ? 'نعم' : 'لا'}`);
    console.log(`✅ المحادثة محفوظة: ${savedChat ? 'نعم' : 'لا'}`);
    console.log(`✅ التحليلات محفوظة: ${savedAnalytics ? 'نعم' : 'لا'}`);

    if (savedUser) {
      console.log(`   - المظهر المحفوظ: ${savedUser.preferences.theme}`);
      console.log(`   - اللغة المحفوظة: ${savedUser.preferences.language}`);
    }

    if (savedChat) {
      console.log(`   - عدد الرسائل المحفوظة: ${savedChat.messages.length}`);
      console.log(`   - حالة الجلسة: ${savedChat.sessionInfo.isActive ? 'نشطة' : 'منتهية'}`);
    }

    // 7. اختبار البحث والاستعلام
    console.log('\n7️⃣ اختبار البحث والاستعلام...');
    
    // البحث عن محادثات المستخدم
    const userChats = await Chat.findUserChats(testUser._id, { section: 'lectures' });
    console.log(`✅ تم العثور على ${userChats.length} محادثة للمستخدم في قسم المحاضرات`);

    // الحصول على إحصائيات المحادثات
    const chatStats = await Chat.getChatStats(testUser._id, 30);
    if (chatStats.length > 0) {
      console.log(`📊 إحصائيات المحادثات (آخر 30 يوم):`);
      console.log(`   - إجمالي المحادثات: ${chatStats[0].totalChats}`);
      console.log(`   - إجمالي الرسائل: ${chatStats[0].totalMessages}`);
      console.log(`   - متوسط الرسائل لكل محادثة: ${chatStats[0].averageMessagesPerChat.toFixed(2)}`);
    }

    // 8. اختبار إحصائيات التخزين
    console.log('\n8️⃣ اختبار إحصائيات التخزين...');
    const storageStats = await getStorageStats();
    if (storageStats) {
      console.log(`📊 إحصائيات التخزين:`);
      console.log(`   - إجمالي المحادثات: ${storageStats.counts.totalChats}`);
      console.log(`   - المحادثات النشطة: ${storageStats.counts.activeChats}`);
      console.log(`   - المحادثات المؤرشفة: ${storageStats.counts.archivedChats}`);
      console.log(`   - إجمالي التحليلات: ${storageStats.counts.totalAnalytics}`);
      console.log(`   - الحجم التقديري: ${storageStats.estimatedSizeKB.total} KB`);
    }

    // 9. اختبار انتهاء صلاحية المحادثات
    console.log('\n9️⃣ اختبار انتهاء صلاحية المحادثات...');
    
    // إنشاء محادثة منتهية الصلاحية للاختبار
    const expiredChat = await Chat.createSession(testUser._id, 'questions', {
      language: 'ar',
      retentionDays: 0 // انتهاء فوري للاختبار
    });
    
    // تحديث تاريخ انتهاء الصلاحية لتكون في الماضي
    expiredChat.expiresAt = new Date(Date.now() - 1000); // منذ ثانية واحدة
    await expiredChat.save();
    
    console.log(`✅ تم إنشاء محادثة منتهية الصلاحية للاختبار`);
    
    // تشغيل تنظيف المحادثات
    const cleanupResult = await cleanupExpiredChats();
    console.log(`🧹 نتيجة التنظيف: ${cleanupResult.message}`);

    // 10. إنهاء الجلسة
    console.log('\n🔟 إنهاء الجلسة...');
    chatSession.endSession();
    await chatSession.save();
    
    console.log(`✅ تم إنهاء الجلسة`);
    console.log(`⏱️ مدة الجلسة: ${chatSession.duration} ثانية`);

    // تنظيف البيانات التجريبية
    console.log('\n🧹 تنظيف البيانات التجريبية...');
    await Analytics.deleteOne({ _id: todayAnalytics._id });
    await Chat.deleteOne({ _id: chatSession._id });
    await User.deleteOne({ _id: testUser._id });
    console.log('✅ تم تنظيف البيانات التجريبية');

    console.log('\n🎉 اكتمل اختبار النظام المستمر بنجاح!');
    console.log('✅ جميع الميزات تعمل بشكل صحيح:');
    console.log('   - حفظ تفضيلات المستخدم ✅');
    console.log('   - حفظ المحادثات مع انتهاء الصلاحية ✅');
    console.log('   - حفظ التحليلات والإحصائيات ✅');
    console.log('   - استمرارية البيانات عبر الجلسات ✅');
    console.log('   - التنظيف التلقائي للبيانات ✅');

  } catch (error) {
    console.error('❌ خطأ في اختبار النظام المستمر:', error);
  } finally {
    await databaseConfig.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الاختبار
testPersistentSystem().catch(console.error);
