const express = require('express');
const { Analytics } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validatePagination } = require('../middleware/validation');
const aiService = require('../services/aiService');

const router = express.Router();

/**
 * الحصول على التحليلات اليومية
 * GET /api/analytics/daily
 */
router.get('/daily', authenticateToken, async (req, res) => {
  try {
    const { date } = req.query;
    const targetDate = date ? new Date(date) : new Date();
    targetDate.setHours(0, 0, 0, 0);

    let analytics = await Analytics.findOne({
      userId: req.user._id,
      date: targetDate
    });

    if (!analytics) {
      // إنشاء سجل جديد إذا لم يكن موجوداً
      analytics = new Analytics({
        userId: req.user._id,
        date: targetDate
      });
      await analytics.save();
    }

    res.json({
      success: true,
      data: {
        analytics: analytics.getDailySummary()
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على التحليلات اليومية:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * الحصول على التحليلات لفترة معينة
 * GET /api/analytics/range
 */
router.get('/range', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate, groupBy = 'day' } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'تاريخ البداية والنهاية مطلوبان'
      });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);

    const analyticsData = await Analytics.getUserAnalytics(req.user._id, start, end);

    // تجميع البيانات حسب الفترة المطلوبة
    let groupedData = [];
    if (groupBy === 'week') {
      groupedData = groupAnalyticsByWeek(analyticsData.analytics);
    } else if (groupBy === 'month') {
      groupedData = groupAnalyticsByMonth(analyticsData.analytics);
    } else {
      groupedData = analyticsData.analytics;
    }

    res.json({
      success: true,
      data: {
        analytics: groupedData,
        summary: analyticsData.summary,
        period: {
          startDate: start,
          endDate: end,
          groupBy
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على تحليلات الفترة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * تحديث النشاط اليومي
 * POST /api/analytics/activity
 */
router.post('/activity', authenticateToken, async (req, res) => {
  try {
    const { activityType, increment = 1, metadata = {} } = req.body;

    if (!activityType) {
      return res.status(400).json({
        success: false,
        message: 'نوع النشاط مطلوب'
      });
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // البحث عن سجل اليوم أو إنشاء واحد جديد
    let analytics = await Analytics.findOne({
      userId: req.user._id,
      date: today
    });

    if (!analytics) {
      analytics = new Analytics({
        userId: req.user._id,
        date: today
      });
    }

    // تحديث النشاط
    analytics.updateActivity(activityType, increment);

    // تحديث الأنماط إذا كانت متوفرة
    if (metadata.category) {
      analytics.updatePattern('categoryPerformance', metadata.category, increment);
    }

    if (metadata.language) {
      analytics.updatePattern('languageUsage', metadata.language, increment);
    }

    if (metadata.difficulty) {
      analytics.updatePattern('preferredDifficulty', metadata.difficulty, increment);
    }

    // تحديث توزيع الوقت
    const hour = new Date().getHours();
    let timeSlot = 'morning';
    if (hour >= 12 && hour < 18) timeSlot = 'afternoon';
    else if (hour >= 18 && hour < 24) timeSlot = 'evening';
    else if (hour >= 0 && hour < 6) timeSlot = 'night';

    analytics.updatePattern('timeDistribution', timeSlot, 1);

    await analytics.save();

    res.json({
      success: true,
      message: 'تم تحديث النشاط بنجاح',
      data: {
        analytics: analytics.getDailySummary()
      }
    });

  } catch (error) {
    console.error('خطأ في تحديث النشاط:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * بدء جلسة جديدة
 * POST /api/analytics/session/start
 */
router.post('/session/start', authenticateToken, async (req, res) => {
  try {
    const { deviceInfo = {} } = req.body;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let analytics = await Analytics.findOne({
      userId: req.user._id,
      date: today
    });

    if (!analytics) {
      analytics = new Analytics({
        userId: req.user._id,
        date: today
      });
    }

    // بدء جلسة جديدة
    const session = analytics.startSession();

    // تحديث معلومات الجهاز
    if (Object.keys(deviceInfo).length > 0) {
      analytics.deviceInfo = {
        ...analytics.deviceInfo,
        ...deviceInfo
      };
    }

    await analytics.save();

    res.json({
      success: true,
      message: 'تم بدء الجلسة بنجاح',
      data: {
        sessionId: session._id,
        startTime: session.startTime
      }
    });

  } catch (error) {
    console.error('خطأ في بدء الجلسة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * إنهاء الجلسة الحالية
 * POST /api/analytics/session/end
 */
router.post('/session/end', authenticateToken, async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const analytics = await Analytics.findOne({
      userId: req.user._id,
      date: today
    });

    if (!analytics) {
      return res.status(404).json({
        success: false,
        message: 'لا توجد جلسة نشطة'
      });
    }

    // إنهاء الجلسة الحالية
    analytics.endCurrentSession();
    await analytics.save();

    res.json({
      success: true,
      message: 'تم إنهاء الجلسة بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إنهاء الجلسة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * الحصول على تحليل الأداء الذكي
 * GET /api/analytics/insights
 */
router.get('/insights', authenticateToken, async (req, res) => {
  try {
    const { period = 30 } = req.query; // 30 يوم افتراضياً

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - period);

    // الحصول على بيانات التحليلات
    const analyticsData = await Analytics.getUserAnalytics(req.user._id, startDate, endDate);

    // إعداد بيانات الأداء للذكاء الاصطناعي
    const performanceData = {
      summary: analyticsData.summary,
      recentAnalytics: analyticsData.analytics.slice(0, 7), // آخر 7 أيام
      userProfile: {
        name: req.user.name,
        level: req.user.level,
        totalPoints: req.user.stats.totalPoints,
        preferences: req.user.preferences
      },
      period: {
        days: period,
        startDate,
        endDate
      }
    };

    // طلب التحليل من الذكاء الاصطناعي
    const analysisResult = await aiService.analyzeStudentPerformance(performanceData, {
      language: req.user.preferences.language || 'ar',
      includeRecommendations: true
    });

    let insights = {};
    if (analysisResult.success) {
      try {
        insights = JSON.parse(analysisResult.content);
      } catch (parseError) {
        console.error('خطأ في تحليل نتيجة الذكاء الاصطناعي:', parseError);
        insights = {
          overallPerformance: {
            score: analyticsData.summary.averageScore || 0,
            grade: 'غير محدد',
            summary: 'تعذر تحليل الأداء'
          },
          strengths: [],
          weaknesses: [],
          recommendations: []
        };
      }
    }

    res.json({
      success: true,
      data: {
        insights,
        rawData: analyticsData.summary,
        period: {
          days: period,
          startDate,
          endDate
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على تحليل الأداء:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * الحصول على الإنجازات
 * GET /api/analytics/achievements
 */
router.get('/achievements', authenticateToken, async (req, res) => {
  try {
    const { period = 30 } = req.query;

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - period);

    const analytics = await Analytics.find({
      userId: req.user._id,
      date: { $gte: startDate, $lte: endDate }
    }).sort('-date');

    // جمع جميع الإنجازات
    const allAchievements = [];
    analytics.forEach(day => {
      day.achievements.forEach(achievement => {
        allAchievements.push({
          ...achievement.toObject(),
          date: day.date
        });
      });
    });

    // ترتيب الإنجازات حسب التاريخ
    allAchievements.sort((a, b) => new Date(b.earnedAt) - new Date(a.earnedAt));

    res.json({
      success: true,
      data: {
        achievements: allAchievements,
        totalAchievements: allAchievements.length,
        totalPoints: allAchievements.reduce((sum, ach) => sum + ach.points, 0)
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الإنجازات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * دوال مساعدة لتجميع البيانات
 */
function groupAnalyticsByWeek(analytics) {
  const weeks = {};
  
  analytics.forEach(day => {
    const weekStart = new Date(day.date);
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    const weekKey = weekStart.toISOString().split('T')[0];
    
    if (!weeks[weekKey]) {
      weeks[weekKey] = {
        weekStart,
        activities: { ...day.activities },
        performance: { ...day.performance }
      };
    } else {
      // جمع الأنشطة
      Object.keys(day.activities).forEach(key => {
        weeks[weekKey].activities[key] += day.activities[key];
      });
      
      // حساب متوسط الأداء
      weeks[weekKey].performance.totalAnswers += day.performance.totalAnswers;
      weeks[weekKey].performance.correctAnswers += day.performance.correctAnswers;
      weeks[weekKey].performance.pointsEarned += day.performance.pointsEarned;
    }
  });
  
  return Object.values(weeks);
}

function groupAnalyticsByMonth(analytics) {
  const months = {};
  
  analytics.forEach(day => {
    const monthKey = day.date.toISOString().substring(0, 7); // YYYY-MM
    
    if (!months[monthKey]) {
      months[monthKey] = {
        month: monthKey,
        activities: { ...day.activities },
        performance: { ...day.performance }
      };
    } else {
      // جمع الأنشطة
      Object.keys(day.activities).forEach(key => {
        months[monthKey].activities[key] += day.activities[key];
      });
      
      // حساب متوسط الأداء
      months[monthKey].performance.totalAnswers += day.performance.totalAnswers;
      months[monthKey].performance.correctAnswers += day.performance.correctAnswers;
      months[monthKey].performance.pointsEarned += day.performance.pointsEarned;
    }
  });
  
  return Object.values(months);
}

module.exports = router;
