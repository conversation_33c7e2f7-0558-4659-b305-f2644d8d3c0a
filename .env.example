# Environment Configuration
NODE_ENV=development
PORT=8000

# Database Configuration - MongoDB Atlas
MONGODB_URI=mongodb+srv://neom:Ahmed13$<EMAIL>/educational_platform?retryWrites=true&w=majority&appName=Cluster0
DB_NAME=educational_platform

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Database Configuration (Future Implementation)
# DATABASE_URL=your-database-url
# DATABASE_NAME=your-database-name

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000

GOOGLE_AI_API_KEY=your-google-ai-api-key
GOOGLE_AI_MODEL=gemini-pro

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,ppt,pptx,txt,jpg,jpeg,png

# Frontend URLs
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001

# API Configuration
API_PREFIX=/api/v1
API_RATE_LIMIT=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
SESSION_SECRET=your-session-secret-key

# Payment Configuration (if needed)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your-ga-id

# Social Media Configuration
FACEBOOK_APP_ID=your-facebook-app-id
GOOGLE_CLIENT_ID=your-google-client-id
TWITTER_API_KEY=your-twitter-api-key

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn

# Development Configuration
DEBUG=true
VERBOSE_LOGGING=true
