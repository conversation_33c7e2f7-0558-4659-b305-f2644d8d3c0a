/**
 * اختبار شامل لجميع APIs
 * Complete API Testing
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5000/api';
let authToken = '';
let testUserId = '';
let testLectureId = '';
let testQuestionId = '';
let testTaskId = '';
let testReportId = '';

async function testAPIs() {
  console.log('🧪 بدء اختبار جميع APIs...\n');

  try {
    // 1. اختبار المصادقة
    console.log('1️⃣ اختبار APIs المصادقة...');
    await testAuthAPIs();

    // 2. اختبار المستخدمين
    console.log('\n2️⃣ اختبار APIs المستخدمين...');
    await testUserAPIs();

    // 3. اختبار المحاضرات
    console.log('\n3️⃣ اختبار APIs المحاضرات...');
    await testLectureAPIs();

    // 4. اختبار الأسئلة
    console.log('\n4️⃣ اختبار APIs الأسئلة...');
    await testQuestionAPIs();

    // 5. اختبار المهام
    console.log('\n5️⃣ اختبار APIs المهام...');
    await testTaskAPIs();

    // 6. اختبار التقارير
    console.log('\n6️⃣ اختبار APIs التقارير...');
    await testReportAPIs();

    // 7. اختبار التحليلات
    console.log('\n7️⃣ اختبار APIs التحليلات...');
    await testAnalyticsAPIs();

    console.log('\n✅ تم اكتمال جميع اختبارات APIs بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار APIs:', error.message);
    if (error.response) {
      console.error('📄 تفاصيل الخطأ:', error.response.data);
    }
  }
}

/**
 * اختبار APIs المصادقة
 */
async function testAuthAPIs() {
  try {
    // تسجيل مستخدم جديد
    const registerData = {
      name: 'مستخدم تجريبي',
      email: `test_${Date.now()}@example.com`,
      password: '123456',
      confirmPassword: '123456'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, registerData);
    console.log('✅ تسجيل مستخدم جديد نجح');
    
    authToken = registerResponse.data.data.token;
    testUserId = registerResponse.data.data.user.id;

    // تسجيل الدخول
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: registerData.email,
      password: registerData.password
    });
    console.log('✅ تسجيل الدخول نجح');

    // الحصول على المستخدم الحالي
    const meResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على المستخدم الحالي نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار المصادقة:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * اختبار APIs المستخدمين
 */
async function testUserAPIs() {
  try {
    // الحصول على لوحة المتصدرين
    const leaderboardResponse = await axios.get(`${BASE_URL}/users/leaderboard`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على لوحة المتصدرين نجح');

    // البحث عن المستخدمين
    const searchResponse = await axios.get(`${BASE_URL}/users/search?q=test`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ البحث عن المستخدمين نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار المستخدمين:', error.response?.data || error.message);
  }
}

/**
 * اختبار APIs المحاضرات
 */
async function testLectureAPIs() {
  try {
    // محاكاة رفع محاضرة (بدون ملف فعلي)
    console.log('⚠️ تخطي رفع المحاضرة (يتطلب ملف)');

    // الحصول على قائمة المحاضرات
    const lecturesResponse = await axios.get(`${BASE_URL}/lectures`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على قائمة المحاضرات نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار المحاضرات:', error.response?.data || error.message);
  }
}

/**
 * اختبار APIs الأسئلة
 */
async function testQuestionAPIs() {
  try {
    // الحصول على قائمة الأسئلة
    const questionsResponse = await axios.get(`${BASE_URL}/questions`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على قائمة الأسئلة نجح');

    // الحصول على إحصائيات الأسئلة
    const statsResponse = await axios.get(`${BASE_URL}/questions/stats/summary`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على إحصائيات الأسئلة نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار الأسئلة:', error.response?.data || error.message);
  }
}

/**
 * اختبار APIs المهام
 */
async function testTaskAPIs() {
  try {
    // الحصول على قائمة المهام
    const tasksResponse = await axios.get(`${BASE_URL}/tasks`);
    console.log('✅ الحصول على قائمة المهام نجح');

    if (tasksResponse.data.data.tasks.length > 0) {
      testTaskId = tasksResponse.data.data.tasks[0].id;
      
      // الحصول على مهمة محددة
      const taskResponse = await axios.get(`${BASE_URL}/tasks/${testTaskId}`);
      console.log('✅ الحصول على مهمة محددة نجح');

      // طلب مساعدة في المهمة
      const helpResponse = await axios.post(`${BASE_URL}/tasks/${testTaskId}/help`, {
        code: 'function test() { return "hello"; }',
        error: 'syntax error'
      }, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ طلب المساعدة في المهمة نجح');
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار المهام:', error.response?.data || error.message);
  }
}

/**
 * اختبار APIs التقارير
 */
async function testReportAPIs() {
  try {
    // إنشاء تقرير جديد
    const reportData = {
      title: 'تقرير تجريبي',
      reportType: 'summary',
      dateRange: {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString()
      }
    };

    const createReportResponse = await axios.post(`${BASE_URL}/reports`, reportData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ إنشاء تقرير جديد نجح');
    
    testReportId = createReportResponse.data.data.report.id;

    // الحصول على قائمة التقارير
    const reportsResponse = await axios.get(`${BASE_URL}/reports`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على قائمة التقارير نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار التقارير:', error.response?.data || error.message);
  }
}

/**
 * اختبار APIs التحليلات
 */
async function testAnalyticsAPIs() {
  try {
    // بدء جلسة جديدة
    const sessionResponse = await axios.post(`${BASE_URL}/analytics/session/start`, {
      deviceInfo: {
        browser: 'Chrome',
        os: 'Windows',
        device: 'Desktop'
      }
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ بدء جلسة جديدة نجح');

    // تحديث النشاط
    const activityResponse = await axios.post(`${BASE_URL}/analytics/activity`, {
      activityType: 'questionsAnswered',
      increment: 1,
      metadata: {
        category: 'web_development',
        difficulty: 'medium'
      }
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ تحديث النشاط نجح');

    // الحصول على التحليلات اليومية
    const dailyResponse = await axios.get(`${BASE_URL}/analytics/daily`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على التحليلات اليومية نجح');

    // الحصول على تحليل الأداء الذكي
    const insightsResponse = await axios.get(`${BASE_URL}/analytics/insights?period=7`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ الحصول على تحليل الأداء الذكي نجح');

    // إنهاء الجلسة
    const endSessionResponse = await axios.post(`${BASE_URL}/analytics/session/end`, {}, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ إنهاء الجلسة نجح');

  } catch (error) {
    console.error('❌ خطأ في اختبار التحليلات:', error.response?.data || error.message);
  }
}

// تشغيل الاختبارات
testAPIs().catch(console.error);
